<?php
// PHP Settings
include_once 'functions/php-ini.php';

// Send PHP errors to Slack
include_once 'services/maintenance/SlackNotifications.php';

// require_once __DIR__ . '/vendor/autoload.php'; // not sure about performance with this

// Add Main javascript and background inline style
include 'functions/enqueue-scripts.php';

// ACF config handler
include_once 'functions/acf-config.php';

// Global brand config handler
include_once 'functions/config.php';

// Global functions
include_once 'functions/global.php';

// Global variables
include_once 'global-variables.php';

// Search mofidications
include_once 'functions/search.php';

// Brand Config - Always prioritize child theme config
if (file_exists(get_stylesheet_directory() . '/services/config.php')) {
    if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
        do_action('qm/info', 'Loading config.php from CHILD theme: ' . get_stylesheet_directory() . '/services/config.php');
    }
    include_once(get_stylesheet_directory() . '/services/config.php');
} elseif (file_exists(get_template_directory() . '/services/config.php')) {
    if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
        do_action('qm/info', 'Loading config.php from PARENT theme: ' . get_template_directory() . '/services/config.php');
    }
    include_once(get_template_directory() . '/services/config.php');
} else {
    if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
        do_action('qm/error', 'No config.php file found in either child or parent theme');
    }
}

// Load Services
require_once('services/loader.php');

// Public functions
include_once 'functions/public.php';
include_once 'functions/datetime.php';
include_once 'functions/vector.php';
include_once 'functions/get-class.php';
include_once 'functions/get-field-tweaked.php';
include_once 'functions/recaptcha.php';
include_once 'functions/seo.php';
// include_once 'functions/tracking.php';
include_once 'functions/acf-backwards-compatibility/acf-backwards.php';
include_once 'functions/acf-auto-class.php';
include_once 'functions/acf-fields-filters.php';
include_once 'functions/performance.php';
include_once 'functions/pnp-tracking.php';

// Player related logic
include_once 'services/player/player_service.php';

// Brand API to get footer links etc.
include_once 'services/other/BrandAPI.php';

// Acquisition related
if (isFeatureActive('campaigns/acq'))
    include 'campaigns/acq/functions.php';

// Dynamic related
if (isFeatureActive('campaigns/dynamic'))
    include 'campaigns/dynamic/functions.php';

// CRM
if (isFeatureActive('campaigns/crm'))
    include 'campaigns/crm/functions.php';

// Start page
if (isFeatureActive('start-page'))
    include 'start-page/functions.php';

// AD Banner
if (isFeatureActive('ad-banner'))
    include 'ad-banner/functions.php';

// Help page
if (isFeatureActive('help-page'))
    include 'help-page/functions.php';

// Responsible gaming
if (isFeatureActive('responsible-gaming'))
    include 'responsible-gaming/functions.php';

// Survey
if (isFeatureActive('survey')) {
    include 'survey/functions.php';
}

// Quiz post type functions
if (isFeatureActive('games/quiz'))
    include 'games/quiz/functions.php';

// Letter game post type
if (isFeatureActive('games/letter-game'))
    include 'games/letter-game/functions.php';

// Casino Games Lib post type
include 'services/casino-games/functions.php';

// SEO Pages
include 'seo-page/functions.php';

// Guides functions
include 'guides/functions.php';

// Define taxonomies on theme switch
include 'functions/add-templates-taxonomy.php';

// Required plugins
include 'functions/required-plugins.php';

// Plugin failsafe mechanism
include 'functions/auto-enable-plugins.php';

// Menus
include 'functions/menus.php';

// Blog
if (isFeatureActive('blog')) {
    include 'blog/functions.php';
}

if (isFeatureActive('api/jackpots')) {
    include 'services/api-jackpots.php';
    include 'shortcodes/get-jackpot.php';
    include 'shortcodes/jackpotometer.php';
}

if (isFeatureActive('api/sportsbook')) {
    include 'services/sportsbook/loader.php';
    include 'shortcodes/get-odds.php';
}

// CTA short code
if (isFeatureActive('shortcodes/cta')) {
    include 'shortcodes/cta.php';
}

// Shortcodes
include 'shortcodes/button.php';
include 'shortcodes/card.php';
include 'shortcodes/dropdown.php';
include 'shortcodes/dynamic-content.php';
include 'shortcodes/player-api.php';

// Admin functions
include_once 'admin/functions-admin.php';

// Gutenberg Blocks includer for Dynamic Pages
include 'functions/include-dynamic-blocks.php';

// Cron jobs
include 'functions/cron-jobs.php';

// WP init
include 'functions/wp-init.php';

include_once 'functions/body-class.php';