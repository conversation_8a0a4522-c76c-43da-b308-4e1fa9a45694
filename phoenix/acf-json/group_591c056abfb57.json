{"key": "group_591c056abfb57", "title": "Page Settings", "fields": [{"key": "field_673e4bfca8c1c", "label": "", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Ⓜ️ <a href=\"https://comeon.atlassian.net/wiki/spaces/PHNX/pages/240222229/Campaign+Settings\" target=\"_blank\">Campaign Settings - Documentation</a>", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_63ca4692471f9", "label": "<PERSON><PERSON>", "name": "hide_header", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-hide_header", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "<PERSON>de", "ui_off_text": "Show", "ui": 1}, {"key": "field_673b159f647e7", "label": "Header Layout", "name": "header_layout", "aria-label": "", "type": "button_group", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_63ca4692471f9", "operator": "!=", "value": "1"}]], "wrapper": {"width": "", "class": "js-header_layout", "id": ""}, "choices": {"default": "<PERSON><PERSON><PERSON>", "start-page": "Start Page"}, "default_value": "default", "return_format": "value", "allow_null": 0, "layout": "horizontal"}, {"key": "field_624d666de7874", "label": "Header Trans<PERSON>ent", "name": "transparent_header", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "js-transparent_header", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Transparent", "ui_off_text": "Normal", "ui": 1}, {"key": "field_67327af6220e6", "label": "Header Transparent - <PERSON> Scrolled", "name": "transparent_header_scrolled", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}, {"field": "field_624d666de7874", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "js-transparent_header_scrolled", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Transparent", "ui_off_text": "Normal", "ui": 1}, {"key": "field_672292464f088", "label": "Header <PERSON>", "name": "hide_header_logo", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "js-hide_header_logo", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "<PERSON>de", "ui_off_text": "Show", "ui": 1}, {"key": "field_672293b7160ab", "label": "Header <PERSON><PERSON>", "name": "hide_header_menu", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "js-hide_header_menu", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "<PERSON>de", "ui_off_text": "Show", "ui": 1}, {"key": "field_67241b1cb45da", "label": "Header CTA Priority", "name": "cta_priority", "aria-label": "", "type": "true_false", "instructions": "Always Show CTA even when user is logged in", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "js-cta_priority", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Show always", "ui_off_text": "<PERSON><PERSON><PERSON>", "ui": 1}, {"key": "field_67241ac4b45d9", "label": "Header CTA Button", "name": "custom_header_cta", "aria-label": "", "type": "true_false", "instructions": "Use custom Header CTA Button?", "required": 0, "conditional_logic": [[{"field": "field_673b159f647e7", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "js-custom_header_cta", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Custom", "ui_off_text": "<PERSON><PERSON><PERSON>", "ui": 1}, {"key": "field_67241d18b525e", "label": "Header CTA Text", "name": "custom_header_cta_text", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_67241ac4b45d9", "operator": "==", "value": "1"}], [{"field": "field_673b159f647e7", "operator": "==", "value": "start-page"}]], "wrapper": {"width": "", "class": "js-custom_header_cta_text", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_67241d6db525f", "label": "Header CTA URL", "name": "custom_header_cta_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_67241ac4b45d9", "operator": "==", "value": "1"}], [{"field": "field_673b159f647e7", "operator": "==", "value": "start-page"}]], "wrapper": {"width": "", "class": "js-custom_header_cta_url", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_67241d81b5260", "label": "Header CTA pretext", "name": "custom_header_cta_pretext", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67241ac4b45d9", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "js-custom_header_cta_pretext", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_64f78e5530f9e", "label": "Main Block Height", "name": "full_screen_content", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-full_screen_content", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Full Screen", "ui_off_text": "Normal Height", "ui": 1}, {"key": "field_6727fc4452276", "label": "Support Button", "name": "support_button", "aria-label": "", "type": "true_false", "instructions": "First enable service in <a href=\"/wp-admin/admin.php?page=brand-settings\" target=\"_blank\">Brand Settings</a>", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-support_button", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Show", "ui_off_text": "<PERSON>de", "ui": 1}, {"key": "field_6391e8d884a5b", "label": "Animation Overlay", "name": "animation_overlay", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-animation_overlay", "id": ""}, "choices": {"snow": "Snow", "spiders": "Spiders"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 1, "ui": 0, "ajax": 0, "placeholder": "", "create_options": 0, "save_options": 0}, {"key": "field_620e2f750fdd2", "label": "Lottie Animation Overlay", "name": "lottie_overlay", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "hidden js-lottie_overlay", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_6391e7efb725d", "label": "Lottie Animation Overlay", "name": "lottie_overlay_file", "aria-label": "", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-lottie_overlay_file", "id": ""}, "return_format": "url", "library": "all", "min_size": "", "max_size": "", "mime_types": ".json"}, {"key": "field_6509fcbc08cc3", "label": "Lottie Animation Mobile Overlay", "name": "lottie_overlay_file_mobile", "aria-label": "", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-lottie_overlay_file js-lottie_overlay_file_mobile", "id": ""}, "return_format": "url", "library": "all", "min_size": "", "max_size": "", "mime_types": ".json"}, {"key": "field_651c8bdbc5111", "label": "Lottie animation Loop", "name": "lottie_overlay_file_loop", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_6391e7efb725d", "operator": "!=empty"}], [{"field": "field_6509fcbc08cc3", "operator": "!=empty"}]], "wrapper": {"width": "", "class": "js-lottie_overlay_file_loop", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_5ba0c7f160d40__trashed", "label": "<PERSON><PERSON>", "name": "footer_scripts", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "visible-for-administrator  js-footer_scripts", "id": ""}, "default_value": "", "maxlength": "", "rows": 4, "placeholder": "", "new_lines": ""}, {"key": "field_62f6151989a16", "label": "Footer Text", "name": "footer_text", "aria-label": "", "type": "wysiwyg", "instructions": "Page specific SEO footer text", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-footer_text", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 1}], "location": [[{"param": "post_type", "operator": "==", "value": "campaign"}], [{"param": "post_type", "operator": "==", "value": "acquisition"}], [{"param": "post_type", "operator": "==", "value": "start-page"}], [{"param": "post_type", "operator": "==", "value": "dynamic"}], [{"param": "post_template", "operator": "==", "value": "page-dynamic-blocks.php"}], [{"param": "post_type", "operator": "==", "value": "seo-page"}], [{"param": "post_type", "operator": "==", "value": "survey"}]], "menu_order": 0, "position": "side", "style": "default", "label_placement": "top", "instruction_placement": "field", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1, "modified": 1755071174}