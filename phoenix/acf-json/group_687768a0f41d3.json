{"key": "group_687768a0f41d3", "title": "Template - Survey", "fields": [{"key": "field_68781647ba011", "label": "Survey Questions", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_687768a154223", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-starting_amount js-title", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Share your feedback", "prepend": "", "append": ""}, {"key": "field_688379f8363e6", "label": "CTA URL", "name": "cta_url", "aria-label": "", "type": "url", "instructions": "Url for the cta button when done with the survey", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-cta_url", "id": ""}, "default_value": "", "allow_in_bindings": 0, "placeholder": ""}, {"key": "field_687768a166794", "label": "Questions", "name": "questions", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-questions", "id": ""}, "layout": "block", "pagination": 0, "min": 1, "max": 0, "collapsed": "field_687768a1b541b", "button_label": "Add Question", "rows_per_page": 20, "sub_fields": [{"key": "field_68780e759e9d7", "label": "Question Type", "name": "type", "aria-label": "", "type": "button_group", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-type", "id": ""}, "choices": {"feedback-slider": "Range Slider", "multi-select-checkbox": "Multi-select", "yes-no-radio": "Yes or No", "textarea": "Text"}, "default_value": "", "return_format": "value", "allow_null": 0, "allow_in_bindings": 0, "layout": "horizontal", "parent_repeater": "field_687768a166794"}, {"key": "field_687768a1b541b", "label": "Question Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-title", "id": ""}, "default_value": "", "maxlength": 100, "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_68780e5b9e9d6", "label": "Question Subtitle", "name": "subtitle", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-subtitle", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_68780f459e9d8", "label": "Range Lower Label", "name": "range_lower", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "feedback-slider"}]], "wrapper": {"width": "50", "class": "js-range_lower", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Not satisfied at all", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_68780f8d9e9d9", "label": "Range Higher Label", "name": "range_higher", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "feedback-slider"}]], "wrapper": {"width": "50", "class": "js-range_higher", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Very satisfied", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_68781103ba00c", "label": "Multi-select Options", "name": "multiselect_options", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "multi-select-checkbox"}]], "wrapper": {"width": "", "class": "js-multiselect_options", "id": ""}, "layout": "table", "min": 2, "max": 0, "collapsed": "", "button_label": "Add Option", "rows_per_page": 20, "sub_fields": [{"key": "field_6878115fba00d", "label": "Option", "name": "option", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-option", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_68781103ba00c"}], "parent_repeater": "field_687768a166794"}, {"key": "field_687811e1ba00e", "label": "Yes Option", "name": "yes_option", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "yes-no-radio"}]], "wrapper": {"width": "50", "class": "js-yes_option", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Yes", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_687815d1ba00f", "label": "No Option", "name": "no_option", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "yes-no-radio"}]], "wrapper": {"width": "50", "class": "js-no_option", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "No", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}, {"key": "field_68781679ba012", "label": "Text Input", "name": "text_input", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_68780e759e9d7", "operator": "==", "value": "textarea"}]], "wrapper": {"width": "", "class": "js-text_input", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Share your experience", "prepend": "", "append": "", "parent_repeater": "field_687768a166794"}]}, {"key": "field_68781633ba010", "label": "Translation", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0, "selected": 0}, {"key": "field_6878187fa5ee5", "label": "Translation", "name": "translation", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-translation", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_687818a4a5ee6", "label": "Next", "name": "next", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-next", "id": ""}, "default_value": "Next", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Next", "prepend": "", "append": ""}, {"key": "field_687818b9a5ee7", "label": "Previous", "name": "previous", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-previous", "id": ""}, "default_value": "Previous", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Previous", "prepend": "", "append": ""}, {"key": "field_687818cea5ee8", "label": "<PERSON><PERSON>", "name": "skip", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-skip", "id": ""}, "default_value": "<PERSON><PERSON>", "maxlength": "", "allow_in_bindings": 0, "placeholder": "<PERSON><PERSON>", "prepend": "", "append": ""}, {"key": "field_68781947a5eed", "label": "Almost done title", "name": "almost_done_title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-almost_done_title", "id": ""}, "default_value": "You’re almost done!", "maxlength": "", "allow_in_bindings": 0, "placeholder": "You’re almost done!", "prepend": "", "append": ""}, {"key": "field_68781992a5eee", "label": "Almost done subtitle - Review Answers", "name": "almost_done_subtitle_review", "aria-label": "", "type": "text", "instructions": "{%d} will be replaced", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-almost_done_subtitle js-almost_done_subtitle_review", "id": ""}, "default_value": "You skipped {%d} optional question. Want to go back and add it?", "maxlength": "", "allow_in_bindings": 0, "placeholder": "You skipped {%d} optional question. Want to go back and add it?", "prepend": "", "append": ""}, {"key": "field_688b38bbca089", "label": "Almost done subtitle - Submit", "name": "almost_done_subtitle_submit", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-almost_done_subtitle_submit", "id": ""}, "default_value": "Good job! You answered all the questions, all that remains is to submit them.", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Good job! You answered all the questions, all that remains is to submit them.", "prepend": "", "append": ""}, {"key": "field_687818efa5ee9", "label": "Review Answers", "name": "review_answers", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-review_answers", "id": ""}, "default_value": "Review Answers", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Review Answers", "prepend": "", "append": ""}, {"key": "field_6878190da5eea", "label": "Submit", "name": "submit", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-submit_now", "id": ""}, "default_value": "Submit Now", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Submit", "prepend": "", "append": ""}, {"key": "field_68781b0c66aa5", "label": "Review Skipped Questions", "name": "review_skipped_questions", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-review_skipped_questions", "id": ""}, "default_value": "Review Skipped Questions", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Review Skipped Questions", "prepend": "", "append": ""}, {"key": "field_68781b5066aa6", "label": "Done Title", "name": "done_title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-done_title", "id": ""}, "default_value": "Thank you for your feedback!", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Thank you for your feedback!", "prepend": "", "append": ""}, {"key": "field_68781b6466aa7", "label": "Done Subtitle", "name": "done_subtitle", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-done_subtitle", "id": ""}, "default_value": "It helps us create a better experience for all our players.", "maxlength": "", "allow_in_bindings": 0, "placeholder": "It helps us create a better experience for all our players.", "prepend": "", "append": ""}, {"key": "field_68781b8e66aa8", "label": "Start Playing", "name": "start_playing", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-start_playing", "id": ""}, "default_value": "Start Playing", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Start Playing", "prepend": "", "append": ""}, {"key": "field_68781937a5eec", "label": "Tap to select", "name": "tap_to_select", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-tap_to_select", "id": ""}, "default_value": "Tap to select", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Tap to select", "prepend": "", "append": ""}, {"key": "field_6878192aa5eeb", "label": "Selected", "name": "selected", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-selected", "id": ""}, "default_value": "Selected", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Selected", "prepend": "", "append": ""}, {"key": "field_68781bb666aa9", "label": "Questions error message", "name": "please_answer_label", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-please_answer_label", "id": ""}, "default_value": "Please answer the question to continue", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Please answer the question to continue", "prepend": "", "append": ""}, {"key": "field_6884c149ad465", "label": "Submit error message", "name": "submit_error_label", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-submit_error_label", "id": ""}, "default_value": "Oops! Something went wrong. Please try again.", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Oops! Something went wrong. Please try again.", "prepend": "", "append": ""}, {"key": "field_689091fb28c12", "label": "Please login to see the survey - Notification", "name": "please_login_notification", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-please_login_notification", "id": ""}, "default_value": "Please login to see the survey", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Please login to see the survey", "prepend": "", "append": ""}]}, {"key": "field_68909afd17162", "label": "Tab Component - RTPG Settings", "name": "", "aria-label": "", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "clone": ["group_62975332339ba"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "survey"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1755073139}