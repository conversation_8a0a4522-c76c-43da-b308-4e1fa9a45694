<?php

// Check if blog
function is_blog() {
    global $wp_query;
    return (get_post_type() == 'post' || is_category() || $wp_query->is_search || is_home());
}

// Check if campaign
function is_campaign() {
    return (in_array(get_post_type(), [ACQUISITION_SLUG, DYNAMIC_SLUG, CAMPAIGN_SLUG, START_PAGE_SLUG, SEO_PAGE_SLUG]) || is_page_template('page-qrcampaign.php') ||  is_page_template('page-dynamic-blocks.php'));
}

function is_game() {
    return (in_array(get_post_type(), [LETTER_GAME_SLUG, QUIZ_SLUG]));
}

function is_responsible_gaming() {
    return (get_post_type() == RESPONSIBLE_GAMING_SLUG);
}

function is_help_page() {
    // Check for help page
    $result = get_post_type() == HELP_PAGE_SLUG;

    // Check for help topic (category)
    if(!$result) {
        if(!empty(get_queried_object()->taxonomy)) {
            $slug = get_queried_object()->taxonomy;
            $result = $slug === HELP_TOPIC_SLUG;
        }
    }

    return $result;
}

function is_survey() {
    return (get_post_type() == SURVEY_SLUG);
}

function is_ad_banner() {
    return (get_post_type() == AD_BANNER_SLUG);
}