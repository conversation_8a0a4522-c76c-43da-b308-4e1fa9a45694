<?php

/**
 * Delete all transients from the database whose keys have a specific prefix.
 *
 * @param string $prefix The prefix. Example: 'px_user_theme_'.
 */
function delete_transients_with_prefix($prefix) {
    foreach (get_transient_keys_with_prefix($prefix) as $key) {
        delete_transient($key);
    }
}

/**
 * Gets all transient keys in the database with a specific prefix.
 *
 * @param  string $prefix Prefix to search for.
 * @return array          Transient keys with prefix, or empty array on error.
 */
function get_transient_keys_with_prefix($prefix) {
    global $wpdb;

    $prefix = $wpdb->esc_like('_transient_' . $prefix);
    $sql    = "SELECT `option_name` FROM $wpdb->options WHERE `option_name` LIKE '%s'";
    $keys   = $wpdb->get_results($wpdb->prepare($sql, $prefix . '%'), ARRAY_A);

    if (is_wp_error($keys)) {
        return [];
    }

    return array_map(function ($key) {
        // Remove '_transient_' from the option name.
        return substr($key['option_name'], strlen('_transient_'));
    }, $keys);
}
