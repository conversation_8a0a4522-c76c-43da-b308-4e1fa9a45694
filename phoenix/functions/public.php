<?php

include 'public/get-url.php';
include 'public/check-page-type.php';
include 'public/die-in-beauty.php';
include 'public/player-segmentation.php';
include 'public/background.php';
include 'public/array-functions.php';
include 'public/geo-location.php';
include 'public/player-id.php';
include 'public/reports.php';
include 'public/transient.php';

// Print error
function printError($errorCode, $errorDescription): never
{
    // Output: Prints a JSON with matching error code.
    $obj = (object)[
        'status' => 'error',
        'errorCode' => $errorCode,
        'errorDescription' => $errorDescription
    ];

    echo json_encode($obj);
    exit;
}

function roundUp($n, $x = 5) {
    return (round($n) % $x === 0) ? round($n) : round(($n + $x / 2 ) / $x ) * $x;
}

function includeLottie() {
    wp_enqueue_script('lottiePlayer', get_parent_theme_file_uri() . '/assets/lottie-player.js', [], '1.3.1', true); //https://unpkg.com/browse/@lottiefiles/lottie-player@1.3.1/dist/lottie-player.js
}

function includeGSAP() {
    // The core GSAP library
    wp_enqueue_script( 'gsap-js', 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js', array(), false, true );
    // ScrollTrigger - with gsap.js passed as a dependency
    wp_enqueue_script( 'gsap-scroll-trigger', 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js', array('gsap-js'), false, true );
}

/**
 * Alter existing table in the database
 *
 * @param string $table_name = 'comeon_beat_the_experts'
 * @param array $newColumns = ['email' => 'varchar(255)', 'odds' => 'varchar(255)'];
 */
function alter_table($table_name, $newColumns){
    foreach ($newColumns as $column => $type) {
        global $wpdb;
        // If $column is missing in the $table_name
        if ($wpdb->get_var("SHOW COLUMNS FROM `$table_name` LIKE '$column'") != $column) {
            do_action('qm/warning', "DB: Cannot find `$column` in table `$table_name`");

            $sql = "ALTER TABLE `$table_name` ADD `$column` $type";
            if ($wpdb->query($sql)) {
                do_action('qm/notice', "DB: Successfully added `$column` in table `$table_name`");
            }
        }
    }
}

function get_the_primary_term($postID, $taxonomy = 'category') {
    $terms = get_the_terms($postID, $taxonomy);

    if (is_array($terms)) {
        return array_shift($terms);  //get the first category
    }

    return null;
}

function uploadMediaFromUrl($url, $filename = '') {

    // Download url to a temp file
    $tmp = download_url($url);
    if (is_wp_error($tmp)) return false;

    // Get the filename and extension ("photo.png" => "photo", "png")
    $filename = $filename ?: pathinfo($url, PATHINFO_FILENAME);
    $extension = pathinfo($url, PATHINFO_EXTENSION);

    // An extension is required or else WordPress will reject the upload
    if (! $extension) {
        // Look up mime type, example: "/photo.png" -> "image/png"
        $mime = mime_content_type($tmp);
        $mime = is_string($mime) ? sanitize_mime_type($mime) : false;

        // Only allow certain mime types because mime types do not always end in a valid extension (see the .doc example below)
        $mime_extensions = [
            // mime_type         => extension (no period)
            'image/jpg'          => 'jpg',
            'image/jpeg'         => 'jpeg',
            'image/gif'          => 'gif',
            'image/png'          => 'png',
        ];

        if (isset($mime_extensions[$mime])) {
            // Use the mapped extension
            $extension = $mime_extensions[$mime];
        } else {
            // Could not identify extension. Clear temp file and abort.
            wp_delete_file($tmp);
            return false;
        }
    }

    // Upload by "sideloading": "the same way as an uploaded file is handled by media_handle_upload"
    // Do the upload
    $attachment_id = media_handle_sideload([
        'name' => "$filename.$extension",
        'tmp_name' => $tmp,
    ]);

    // Clear temp file
    wp_delete_file($tmp);

    // Error uploading
    if (is_wp_error($attachment_id)) return false;

    // Success, return attachment ID
    return (int) $attachment_id;
}