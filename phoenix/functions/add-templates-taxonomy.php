<?php
function add_templates_taxonomy()
{
    // ACQ
    if (isFeatureActive('campaigns/acq')) {
        wp_insert_term('Jackpot Games', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'jackpot-games']); // Jackpot Games
        wp_insert_term('Info Page', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'info-page']); // Info Page (aka Landing Page Prototype)
        wp_insert_term('Hot/Cold Jackpot', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'hot-cold-jackpot']); // Wheel of Change
        wp_insert_term('Centered - Button', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'centered-button']); // Centered Button
        wp_insert_term('Left and Right - Button', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-button']); // Left and Right - Button
        wp_insert_term('Left and Right - Countdown', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-countdown']); // Left and Right - Countdown
        wp_insert_term('Left and Right - Video', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-video']); // Left and Right - Video
        wp_insert_term('Left and Right - Optin', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-optin']); // Left and Right - Optin
        wp_insert_term('Offer Cards', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'offer-cards']); // Offer Cards
        wp_insert_term('Personal Slots Recommendation', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'personal-slot-recommendation']); // Personal Slots Recommendation
        wp_insert_term('Casinokollen', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'casinokollen']); // Casinokollen
        wp_insert_term('Beat the Expert - Optin', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'beat-the-expert-optin']); // Beat the Expert - Optin
        wp_insert_term('Promo Hub', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'promo-hub']); // Promo Hub
        // wp_insert_term('Quick Registration/Login', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'quick-register-login']); // Quick Registration/Login
        wp_insert_term('Brackets', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'brackets']); // Brackets
        wp_insert_term('Weekly Triple', ACQUISITION_TEMPLATE_SLUG, ['description' => '', 'slug' => 'weekly-triple']); // Weekly Triple
    }

    // CRM
    if (isFeatureActive('campaigns/crm')) {
        wp_insert_term('Left and Right', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right']); //  Left and Right - United
        // wp_insert_term('Left and Right - Beat the Expert', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-beat-the-expert']); // Beat the Expert
        wp_insert_term('Left and Right - Segmented', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'left-and-right-segmented']); // Left and Right - Segmented
        wp_insert_term('Interactive Campaign', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'interactive']); // Interactive Campaign
        wp_insert_term('Wheel of Chance', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'wheel']); // Wheel of Change
        wp_insert_term('Offer of the Day', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'offer-of-the-day']); // Offer of the Day
        wp_insert_term('Calendar Offers', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'calendar-offers']); // Calendar Offer
        wp_insert_term('Beat the Expert 2.0', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'beat-the-expert']); // Beat the Expert 2.0
        wp_insert_term('Money Drop', CAMPAIGN_TEMPLATE_SLUG, ['description' => '', 'slug' => 'money-drop']); // Money Drop
    }

    // Start Page
    if (isFeatureActive('start-page')) {
        // wp_insert_term('Normal Start Page', START_PAGE_TEMPLATE_SLUG, ['description' => '', 'slug' => 'normal']); // Normal Start Page
        // wp_insert_term('PNP Page', START_PAGE_TEMPLATE_SLUG, ['description' => '', 'slug' => 'pnp']); // PNP Start Page
        wp_insert_term('Dynamic Page', START_PAGE_TEMPLATE_SLUG, ['description' => '', 'slug' => 'dynamic']); // Dynamic Start Page
    }

    flush_rewrite_rules();
    do_action('qm/notice', 'Taxonomies succesfully repaired');
}
add_action('after_switch_theme', 'add_templates_taxonomy');
