<?php
if(isFeatureActive('campaigns/crm/beat-the-expert')) {
    include(locate_template('campaigns/crm/beat-the-expert/functions.php', false, false));
}

if(isFeatureActive('campaigns/crm/left-and-right-beat-the-expert')) {
    include(locate_template('campaigns/crm/left-and-right-beat-the-expert/functions.php', false, false));
}

if(isFeatureActive('campaigns/crm/money-drop')) {
    include(locate_template('campaigns/crm/money-drop/functions.php', false, false));
}

add_action('init', 'create_post_type_campaign');
function create_post_type_campaign()
{
    $labels = [
        'name'                  => _x('Customer Retention Management', 'Post general name'),
        'singular_name'         => _x('CRM', 'Post singular name'),
        'add_new'               => __('Create New CRM Page'),
        'add_new_item'          => __('Create New CRM Page'),
        'edit_item'             => __('Edit CRM Page'),
        'new_item'              => __('Create CRM Page'),
        'all_items'             => __('All CRM Pages'),
        'view_item'             => __('View CRM Page'),
        'search_items'          => __('Search CRM Pages'),
        'not_found'             => __('No CRM Pages found'),
        'not_found_in_trash'    => __('No CRM Pages found in the Trash'),
        'parent_item_location'  => '',
        'menu_name'             => 'CRM',
    ];

    $args = [
        'labels'                => $labels,
        'description'           => 'Contains all CRM pages and relevant data',
        'public'                => true,
        'publicly_queryable'    => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'show_in_rest'          => true,
        'exclude_from_search'   => true,
        'rest_base'             => 'crm',
        'query_var'             => true,
        'menu_position'         => 1,
        'supports'              => ['title', 'custom-fields', 'revisions'],
        'has_archive'           => true,
        'menu_icon'             => 'dashicons-groups',
        'taxonomies'            => [CAMPAIGN_TEMPLATE_SLUG, CAMPAIGN_TAG_SLUG],
    ];

    register_post_type(CAMPAIGN_SLUG, $args);
}

// Custom messages for Campaigns
add_filter('post_updated_messages', 'create_custom_messages_campaign');
function create_custom_messages_campaign($messages)
{
    global $post;
    global $post_ID;

    $messages[CAMPAIGN_SLUG] = [
        0 => '',
        1 => sprintf(__('Campaign updated. <a href="%s">View campaign</a>'), esc_url(get_permalink($post_ID))),
        2 => __('Custom field updated.'),
        3 => __('Custom field deleted.'),
        4 => __('Campaign updated.'),
        5 => isset($_GET['revision']) ? sprintf(__('Campaign restored to revision from %s'), wp_post_revision_title((int) $_GET['revision'], false)) : false,
        6 => sprintf(__('Campaign published. <a href="%s">View campaign</a>'), esc_url(get_permalink($post_ID))),
        7 => __('Campaign saved.'),
        8 => sprintf(__('Campaign submitted. <a target="_blank" href="%s">Preview campaign</a>'), esc_url(add_query_arg('preview', 'true', get_permalink($post_ID)))),
        9 => sprintf(__('Campaign scheduled for: <strong>%1$s</strong>. <a target="_blank" href="%2$s">Preview campaign</a>'), date_i18n(__('M j, Y @ G:i'), strtotime((string) $post->post_date)), esc_url(get_permalink($post_ID))),
        10 => sprintf(__('Campaign draft updated. <a target="_blank" href="%s">Preview campaign</a>'), esc_url(add_query_arg('preview', 'true', get_permalink($post_ID)))),
    ];

    return $messages;
}

// Custom taxonomy "Templates" - used for custom post type "Campaigns"
add_action('init', 'create_taxonomy_template');
function create_taxonomy_template()
{
    $labels = [
        'name' => _x('Templates', 'Taxonomy general name'),
        'singular_name' => _x('Template', 'Taxonomy singular name'),
        'search_items' => __('Search Templates'),
        'all_items' => __('All Templates'),
        'parent_item' => __('Parent Template'),
        'parent_item_colon' => __('Parent Template:'),
        'edit_item' => __('Edit Template'),
        'update_item' => __('Update Template'),
        'add_new_item' => __('Add New Template'),
        'new_item_name' => __('New Template'),
        'menu_name' => __('Templates'),
    ];
    $args = [
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'meta_box_cb' => false,
    ];

    register_taxonomy(CAMPAIGN_TEMPLATE_SLUG, CAMPAIGN_SLUG, $args);
}

// Taxonomy "Tags" - used for custom post type "Campaigns"
add_action('init', 'create_taxonomy_tags');
function create_taxonomy_tags()
{
    $args = [
        'hierarchical' => false,
        'label' => __('Tags'),
        'singular_name' => __('Tag'),
        'rewrite' => true,
        'query_var' => true
    ];

    register_taxonomy(CAMPAIGN_TAG_SLUG, CAMPAIGN_SLUG, $args);
}

// Filter campaigns by templates
add_action('restrict_manage_posts', 'filter_by_template');
function filter_by_template()
{
    global $typenow;

    $post_type = CAMPAIGN_SLUG;
    $taxonomy = CAMPAIGN_TEMPLATE_SLUG;
    if ($typenow == $post_type) {
        $selected      = $_GET[$taxonomy] ?? '';
        $info_taxonomy = get_taxonomy($taxonomy);
        wp_dropdown_categories([
            'show_option_all' => __("Show All {$info_taxonomy->label}"),
            'taxonomy'        => $taxonomy,
            'name'            => $taxonomy,
            'orderby'         => 'name',
            'selected'        => $selected,
            'show_count'      => true,
            'hide_empty'      => true,
            'rewrite' => ['slug' => CAMPAIGN_TEMPLATE_SLUG],
        ]);
    };
}

// Display filtered templates
add_filter('parse_query', 'display_filtered_template');
function display_filtered_template($query)
{
    global $pagenow;

    $post_type = CAMPAIGN_SLUG;
    $taxonomy = CAMPAIGN_TEMPLATE_SLUG;
    $q_vars = &$query->query_vars;

    if ($pagenow == 'edit.php' && isset($q_vars['post_type']) && $q_vars['post_type'] == $post_type && isset($q_vars[$taxonomy]) && is_numeric($q_vars[$taxonomy]) && $q_vars[$taxonomy] != 0) {
        $term = get_term_by('id', $q_vars[$taxonomy], $taxonomy);
        $q_vars[$taxonomy] = $term->slug;
    }
}

// Creating a "Select Template interface" for campaigns
add_action('add_meta_boxes', 'render_select_template_meta_box', 0);
function render_select_template_meta_box()
{
    // add_meta_box( 'select-template-meta-box', __( 'Select Template' ), 'populate_select_template',
    //   CAMPAIGN_SLUG, 'core', 'high' );
    add_meta_box('select-template-meta-box', __('Select Template'), 'populate_select_template', CAMPAIGN_SLUG);
}

// Defining the content of the meta box
function populate_select_template($post)
{
    $taxonomy = CAMPAIGN_TEMPLATE_SLUG;
    $taxonomy_terms = get_terms($taxonomy, ['hide_empty' => 0, 'orderby' => 'count', 'order' => 'DESC']);
    $saved_term = get_the_primary_term($post->ID, $taxonomy);
    $saved_template_id = null;
    if (!empty($saved_term)) {
        $saved_template_id = $saved_term->term_id;
    }
?>

    <div id="taxonomy-template" class="categorydiv">
        <select style="width: 100%; height: 64px; font-size: 21px;" name="tax_input[template][]" id="templatechecklist" data-wp-lists="list:template" class="categorychecklist form-no-clear">
            <?php if (!$saved_template_id) : ?>
                <option value="" selected disabled hidden>Choose here</option>
            <?php endif;
            foreach ($taxonomy_terms as $term):
                $isActive = isFeatureActive('campaigns/crm/' . $term->slug); ?>
                <option <?= ($isActive) ? '' : 'disabled' ?> value="<?= $term->term_id; ?>"
                    <?= ($saved_template_id === $term->term_id) ? ' selected' : false; ?>
                    title="<?= ($isActive) ? '' : 'Not available for your brand yet' ?>"
                ><?= ($isActive) ? '' : '*' ?> <?= $term->name; ?>
                </option>
            <?php endforeach; ?>
        </select>
        <p>
            * Not available for your brand yet
        </p>
    </div>

<?php
}

// Remove 'campaign' from permalink -> occurs during campaign creation
add_filter('post_type_link', 'remove_campaign_slug', 10, 3);
function remove_campaign_slug($post_link, $post, $leavename)
{
    if (CAMPAIGN_SLUG != $post->post_type || 'publish' != $post->post_status)
        return $post_link;

    $post_link = str_replace('/' . $post->post_type . '/', '/', (string) $post_link);
    return $post_link;
}


// Migrate templates from CRM to ACQ
// scripts (we need them to be executed one time)
//
// include "migrate-scripts.php";
//
// We can possibly bring this migration tool under Phoenix Toolkit menu on admin bar, if we think we might need