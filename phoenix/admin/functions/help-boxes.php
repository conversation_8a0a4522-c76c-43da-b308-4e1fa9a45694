<?php
define('PHOENIX_PAGE', 'https://comeon.atlassian.net/wiki/spaces/PHNX/overview');
define('PHOENIX_HELP_PAGES', [
    'start-page' =>  [
        'pnp' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/57770310/PNP+Start+Page',
        'dynamic' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58196155/Dynamic+Start+Page'
    ],
    'go-pages' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58622406/SEO+Pages',
    'help-page' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58196148/FAQ+-+Help+Pages',
    'survey' => '', //! this is not set yet, need to create a guide
    'responsible-gaming' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58622413/Responsible+Gaming+Wordpress+Pages',
    'blog'  => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/124747777/Blog',
    'games' => [
        'quiz'        => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58196141/Quiz',
        'letter-game' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/57770303/Letter+Game'
    ],
    'campaigns' => [
        'acq' => [
            'jackpot-games'                     => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99844107/Jackpot+Games',
            'playngo-jackpot-games'             => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99844107/Jackpot+Games',
            'info-page'                         => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99614769/Info+Page',
            'hot-cold-jackpot'                  => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99385388/Hot+Cold+Jackpot',
            'centered-button'                   => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/98828479/Centered+Button',
            'left-and-right-button'             => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99614776/Left+and+Right+Button',
            'left-and-right-countdown'          => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99942462/Left+and+Right+Countdown',
            'left-and-right-optin'              => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99451000/Left+and+Right+Optin',
            'offer-cards'                       => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99876882/Offer+Cards',
            'personal-slot-recommendation'      => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99942469/Personal+Slots+Recommendation',
            'casinokollen'                      => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99942439/Casinokollen',
            'beat-the-expert-optin'             => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99778600/Beat+the+Expert+BTE+2.0',
            'promo-hub'                         => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99844125/Promo+Hub',
            // 'quick-register-login'              => '',
            'brackets'                          => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/63307846/Brackets',
        ],
        'dynamic' => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/98566332/Dynamic+CAM',
        'crm' => [
            'beat-the-expert'                   => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99778600/Beat+the+Expert+BTE+2.0',
            'left-and-right'                    => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99418190/Left+and+Right',
            'left-and-right-beat-the-expert'    => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99778600/Beat+the+Expert+BTE+2.0',
            'interactive'                       => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99647523/Interactive',
            'wheel'                             => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99483727/Wheel+of+Chance',
            'offer-of-the-day'                  => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/99844100/Offer+of+the+Day',
            'calendar-offers'                   => 'https://comeon.atlassian.net/wiki/spaces/PKB/pages/58229016/Calendar+Offers'
        ],
    ],
    'components' => [
        'leaderboard' => 'https://comeon.atlassian.net/wiki/spaces/PHNX/pages/119799824/Leaderboard+-+Component',
    ]
]);

function echoMessage() {
    echoLink(PHOENIX_PAGE, 'Phoenix Team');
}

function echoLink($url, $label) {
    echo sprintf('<p>🔗 <a href="%s" target="_blank">%s</a></p>', $url, $label);
}

function help_link_cam($post) {
    echoMessage();

    $primaryTerm = get_the_primary_term($post, ACQUISITION_TEMPLATE_SLUG);
    if (!empty($primaryTerm->slug) && !empty(PHOENIX_HELP_PAGES['campaigns']['acq'][$primaryTerm->slug])) {
        echoLink(PHOENIX_HELP_PAGES['campaigns']['acq'][$primaryTerm->slug], $primaryTerm->name . ' Guide');
    }
}

function help_link_dyn($post) {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['campaigns']['dynamic'], 'Dynamic CAM Guide');
}

function help_link_crm($post) {
    echoMessage();

    $primaryTerm = get_the_primary_term($post, CAMPAIGN_TEMPLATE_SLUG);
    if (!empty($primaryTerm->slug) && !empty(PHOENIX_HELP_PAGES['campaigns']['crm'][$primaryTerm->slug])) {
        echoLink(PHOENIX_HELP_PAGES['campaigns']['crm'][$primaryTerm->slug], $primaryTerm->name . ' Guide');
    }
}

function help_link_blog() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['blog'], 'Blog Guide');
}

function help_link_quiz() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['games']['quiz'], 'Quiz Guide');
}

function help_link_letter_game() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['games']['letter-game'], 'Letter Game Guide');
}

function help_link_responsible_gaming() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['responsible-gaming'], 'Responsible Gaming Guide');
}

function help_link_help_page() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['help-page'], 'Help Page Guide');
}

function help_link_survey() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['survey'], 'Survey Guide');
}

function help_link_page($post) {
    echoMessage();

    $pageType = get_page_template_slug($post);
    if($pageType === '') {
        echoLink(PHOENIX_HELP_PAGES['go-pages'], 'Go Pages');
    }
}

function help_link_start_page($post) {
    echoMessage();

    $primaryTerm = get_the_primary_term($post, START_PAGE_TEMPLATE_SLUG);
    if (!empty($primaryTerm->slug) && !empty(PHOENIX_HELP_PAGES['start-page'][$primaryTerm->slug])) {
        echoLink(PHOENIX_HELP_PAGES['start-page'][$primaryTerm->slug], $primaryTerm->name . ' Guide');
    }

}

function help_link_component_leaderboard() {
    echoMessage();
    echoLink(PHOENIX_HELP_PAGES['components']['leaderboard'], 'Leaderboard Guide');
}

function add_help_meta_box() {
    $helpBoxTitle = 'Useful Links';

    add_meta_box('help-meta-box-cam', $helpBoxTitle, 'help_link_cam', ACQUISITION_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-dyn', $helpBoxTitle, 'help_link_dyn', DYNAMIC_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-crm', $helpBoxTitle, 'help_link_crm', CAMPAIGN_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-blog', $helpBoxTitle, 'help_link_blog', 'post', 'side', 'high');
    add_meta_box('help-meta-box-quiz', $helpBoxTitle, 'help_link_quiz', QUIZ_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-letter-game', $helpBoxTitle, 'help_link_letter_game', LETTER_GAME_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-responsible-gaming', $helpBoxTitle, 'help_link_responsible_gaming', RESPONSIBLE_GAMING_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-help-page', $helpBoxTitle, 'help_link_help_page', HELP_PAGE_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-survey', $helpBoxTitle, 'help_link_survey', SURVEY_SLUG, 'side', 'high');
    add_meta_box('help-meta-box-page', $helpBoxTitle, 'help_link_page', 'page', 'side', 'high');
    add_meta_box('help-meta-box-start-page', $helpBoxTitle, 'help_link_start_page', START_PAGE_SLUG, 'side', 'high');
    if (defined('COMEON_LEADERBOARD_SLUG')) {
        add_meta_box('help-meta-box-start-page', $helpBoxTitle, 'help_link_component_leaderboard', COMEON_LEADERBOARD_SLUG, 'side', 'high');
    }
}

add_action('add_meta_boxes', 'add_help_meta_box');