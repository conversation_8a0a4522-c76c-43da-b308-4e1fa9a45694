<?php
// Testing tools Options page
add_action('admin_init', 'handle_testing_theme_selector', 1);
function handle_testing_theme_selector() {
    global $pagenow;
    if (
        current_user_can('switch_themes') && is_admin() && 'admin.php' === $pagenow &&
        isset($_GET['testing_action']) && isset($_GET['page']) &&
        'acf-options-testing-tools' === $_GET['page']
    ) {
        $theme = wp_get_theme($_GET['stylesheet']);

        if (!$theme->exists() || !$theme->is_allowed()) {
            wp_die(
                '<h1>' . __('Something went wrong.') . '</h1>' .
                    '<p>' . __('The requested theme does not exist.') . '</p>',
                403
            );
        }

        if ($_GET['testing_action'] === 'Change' || ($_GET['testing_action'] === 'Change for everyone' && (DEV_ENV || LOCAL_ENV))) {
            switch_theme($theme->get_stylesheet()); // Switch theme globally

        } elseif ($_GET['testing_action'] === 'Change only for me'  && (DEV_ENV || LOCAL_ENV)) {
            // Store theme in transient for current user
            $user_id = get_current_user_id();
            set_transient('px_user_theme_' . $user_id, $theme->get_stylesheet(), 30 * DAY_IN_SECONDS); // 30 days expiry
        }

        wp_redirect(admin_url('admin.php?page=acf-options-testing-tools'));
        exit();
    }
}

add_action('switch_theme', 'handle_theme_switch');
function handle_theme_switch($themeName) {
    delete_transients_with_prefix('px_user_theme_');
}

add_action('brand-settings_page_acf-options-testing-tools', 'after_options_testing_tools_page_content', 20);
function after_options_testing_tools_page_content() {
    // Minor SQL optimizations (First run applies optimizations, second run reverts it back)
    if (isset($_GET['optimize-db'])) {
        global $wpdb;

        $checkAutoloadForFirstMinorOptimization = $wpdb->get_results("SELECT autoload FROM $wpdb->options WHERE option_name = 'can_compress_scripts' LIMIT 1", 'ARRAY_A');
        // If autoload is no, it updates as yes, and visa versa to fix any unexpected problems quickly
        if ($checkAutoloadForFirstMinorOptimization[0]['autoload'] === "no") {
            $wpdb->get_results("UPDATE $wpdb->options SET autoload = 'yes' WHERE option_name = 'can_compress_scripts'");

            echo <<<CanCompressScriptsSetToYes
            <script>
                jQuery(document).ready(function() {
                    jQuery('.acf-settings-wrap h1').after('<div class="notice notice-success"><p>(FIX DONE) Autoload for the option "can_compress_scripts" set to "yes"</p></div>');
                });
            </script>
            CanCompressScriptsSetToYes;
        } else if ($checkAutoloadForFirstMinorOptimization[0]['autoload'] === "yes") {
            $wpdb->get_results("UPDATE $wpdb->options SET autoload = 'no' WHERE option_name = 'can_compress_scripts'");
            echo <<<CanCompressScriptsSetToNo
            <script>
                jQuery(document).ready(function() {
                    jQuery('.acf-settings-wrap h1').after('<div class="notice notice-success"><p>(REVERT DONE) Autoload for the option "can_compress_scripts" set to "no"</p></div>');
                });
            </script>
            CanCompressScriptsSetToNo;
        }

        $checkAutoloadForMediumCrop      = $wpdb->get_results("SELECT option_id FROM $wpdb->options WHERE option_name = 'medium_crop' LIMIT 1", 'ARRAY_A');
        $checkAutoloadForLargeCrop       = $wpdb->get_results("SELECT option_id FROM $wpdb->options WHERE option_name = 'large_crop' LIMIT 1", 'ARRAY_A');
        $checkAutoloadForMediumLargeCrop = $wpdb->get_results("SELECT option_id FROM $wpdb->options WHERE option_name = 'medium_large_crop' LIMIT 1", 'ARRAY_A');

        // Add crop settings to options table if they don't exist
        if (NULL === $checkAutoloadForMediumCrop[0]['option_id']) {
            $wpdb->query("INSERT INTO $wpdb->options SET option_name = 'medium_crop', option_value = '0', autoload = 'yes'");
        } else {
            $wpdb->query("DELETE FROM $wpdb->options WHERE option_name = 'medium_crop'");
        }
        if (NULL === $checkAutoloadForLargeCrop[0]['option_id']) {
            $wpdb->query("INSERT INTO $wpdb->options SET option_name = 'large_crop', option_value = '0', autoload = 'yes'");
        } else {
            $wpdb->query("DELETE FROM $wpdb->options WHERE option_name = 'large_crop'");
        }
        if (NULL === $checkAutoloadForMediumLargeCrop[0]['option_id']) {
            $wpdb->query("INSERT INTO $wpdb->options SET option_name = 'medium_large_crop', option_value = '0', autoload = 'yes'");

            echo <<<MediaCropFixesDone
            <script>
                jQuery(document).ready(function() {
                    jQuery('.acf-settings-wrap h1').after('<div class="notice notice-success"><p>(FIX DONE) Media crop options added into wp_options</p></div>');
                });
            </script>
            MediaCropFixesDone;
        } else {
            $wpdb->query("DELETE FROM $wpdb->options WHERE option_name = 'medium_large_crop'");

            echo <<<MediaCropFixesReverted
            <script>
                jQuery(document).ready(function() {
                    jQuery('.acf-settings-wrap h1').after('<div class="notice notice-success"><p>(REVERT DONE) Media crop options deleted from wp_options</p></div>');
                });
            </script>
            MediaCropFixesReverted;
        }
    }
?>
    <div class="testing-option-page wrap">
        <div class="testing-option-page__theme-selector">
            <h3>Theme / Brand</h3>
            <?php
            $allThemes = wp_get_themes();
            if (!empty($allThemes)) : ?>
                <form action="" method="get">
                    <input type="hidden" name="page" value="acf-options-testing-tools" />
                    <select name="stylesheet">
                        <?php foreach ($allThemes as $theme) : ?>
                            <?php if (!empty($theme->parent())) : ?>
                                <?php $active = (CURRENT_BRAND == $theme->get_stylesheet()); ?>
                                <option value="<?= $theme->get_stylesheet(); ?>" <?= ($active ? ' selected="selected"' : ''); ?>><?= $theme->name; ?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    <?php if (DEV_ENV || LOCAL_ENV) : ?>
                        <input type="submit" class="button button-primary button-large" name="testing_action" value="Change only for me" />
                        <input type="submit" class="button button-secondary button-large" name="testing_action" value="Change for everyone" />
                    <?php else : ?>
                        <input type="submit" class="button button-primary button-large" name="testing_action" value="Change" />
                    <?php endif; ?>
                </form>
                <?php if (DEV_ENV || LOCAL_ENV) : ?>
                    <p><small>Note: Personal brand change will be overridden when it is changed for everyone.</small></p>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <br />
        <div class="testing-option-page__buttons">
            <h3>Quick Test Pages (in new tabs)</h3>
            <?php if (isFeatureActive('campaigns/acq')) { ?>
                <a href="javascript:void(0)" id="open-campaign-testing-acq" class="button button-primary button-large">Acquisition templates *</a>
                <pre id="testing-campaigns-acq" class="hidden">
                    <?php
                    global $configArray;
                    foreach ($configArray['campaigns']['acq'] as $templateSlug => $templateActive) {
                        if ($templateActive) {
                            print_r(get_site_url() . '/' . $templateSlug . PHP_EOL);
                        }
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('campaigns/crm')) { ?>
                <a href="javascript:void(0)" id="open-campaign-testing-crm" class="button button-primary button-large">CRM templates *</a>
                <pre id="testing-campaigns-crm" class="hidden">
                    <?php
                    global $configArray;
                    foreach ($configArray['campaigns']['crm'] as $templateSlug => $templateActive) {
                        if ($templateActive) {
                            print_r(get_site_url() . '/' . $templateSlug . PHP_EOL);
                        }
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('blog')) { ?>
                <a href="javascript:void(0)" id="open-testing-blog" class="button button-primary button-large">Blog pages</a>
                <pre id="testing-blog" class="hidden">
                    <?php
                    print_r(get_permalink(get_option('page_for_posts')) . PHP_EOL); // main blog link

                    $args = [
                        'taxonomy' => 'category',
                        'number' => 1
                    ];
                    $categories = get_terms($args);
                    $category = array_shift($categories);
                    if (!empty($category)) {
                        print_r(get_term_link($category) . PHP_EOL); // first category link
                    }

                    $args = [
                        'post_type' => 'post',
                        'posts_per_page' => 1,
                        'order' => 'ASC'
                    ];
                    $posts = get_posts($args);
                    if (!empty($posts[0])) {
                        print_r(get_permalink($posts[0]) . PHP_EOL); // first post link
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('games/quiz')) { ?>
                <a href="javascript:void(0)" id="open-testing-quiz" class="button button-primary button-large">Quiz pages</a>
                <pre id="testing-quiz" class="hidden">
                    <?php
                    print_r(get_post_type_archive_link(QUIZ_SLUG) . PHP_EOL); // archive quiz link

                    $args = [
                        'post_type' => QUIZ_SLUG,
                        'posts_per_page' => 1,
                        'order' => 'ASC'
                    ];
                    $posts = get_posts($args);
                    if (!empty($posts[0])) {
                        print_r(get_permalink($posts[0]) . PHP_EOL); // first quiz link
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('games/letter-game')) { ?>
                <a href="javascript:void(0)" id="open-testing-letter-game" class="button button-primary button-large">Letter Game pages</a>
                <pre id="testing-letter-game" class="hidden">
                    <?php
                    print_r(get_post_type_archive_link(LETTER_GAME_SLUG) . PHP_EOL); // archive letter games link

                    $args = [
                        'post_type' => LETTER_GAME_SLUG,
                        'posts_per_page' => 1,
                        'order' => 'ASC'
                    ];
                    $posts = get_posts($args);
                    if (!empty($posts[0])) {
                        print_r(get_permalink($posts[0]) . PHP_EOL); // first letter game link
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('start-page')) { ?>
                <a href="javascript:void(0)" id="open-testing-start-pages" class="button button-primary button-large">Start pages *</a>
                <pre id="testing-start-pages" class="hidden">
                    <?php
                    $args = ['taxonomy' => START_PAGE_TEMPLATE_SLUG];
                    $templates = get_terms($args);
                    foreach ($templates as $template) {
                        print_r(get_site_url() . '/' . START_PAGE_SLUG . '/' . $template->slug . PHP_EOL); // first help topic link
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <?php if (isFeatureActive('help-page')) { ?>
                <a href="javascript:void(0)" id="open-testing-help-pages" class="button button-primary button-large">Help pages (FAQ)</a>
                <pre id="testing-help-pages" class="hidden">
                    <?php
                    print_r(get_post_type_archive_link(HELP_PAGE_SLUG) . PHP_EOL); // main help page link

                    $args = [
                        'taxonomy' => HELP_TOPIC_SLUG,
                        'number' => 1
                    ];
                    $categories = get_terms($args);
                    $category = array_shift($categories);
                    if (!empty($category)) {
                        print_r(get_term_link($category) . PHP_EOL); // first help topic link
                    }

                    $args = [
                        'post_type' => HELP_PAGE_SLUG,
                        'posts_per_page' => 1,
                        'order' => 'ASC'
                    ];
                    $posts = get_posts($args);
                    if (!empty($posts[0])) {
                        print_r(get_permalink($posts[0]) . PHP_EOL); // first help page link
                    }
                    ?>
                </pre>
            <?php
            } ?>

            <a href="javascript:void(0)" id="open-testing-pages" class="button button-secondary button-large">Additional pages</a>

            <p>* - template pages should have the permalink set as the template-slug <i>(example: /left-and-right-button)</i></p>
            <p><small>Your browser may block opening the tabs, please make sure to allow pop-ups for this page!</small></p>
        </div>

        <style>
            .theme-configs {
                display: flex;
                width: 100%;
                max-width: 100%;
                flex-wrap: wrap;
            }

            .theme-configs .postbox.acf-postbox {
                min-height: 100%;
                max-width: 400px;
                flex: 1;
                padding: 0px 10px;
            }


            .theme-configs h2 {
                text-align: center;
                margin: 8px auto;
                position: sticky;
                top: 40px;
                width: 100%;
                background-color: #fff;
                border: 1px solid #777;
                padding: 10px 0;
                z-index: 1;
            }

            .config-container ul {
                padding-inline-start: 10px;
            }
        </style>

        <br />
        <br />

        <h3>Config files</h3>
        <div class="theme-configs">
            <?php
            $configJson = file_get_contents(get_theme_root() . "/" . $currentTheme . "/services/config.json");
            $configJson = json_decode($configJson, true);
            sort_alpha_r($configJson);
            $configHTMLTree = buildHtmlList($configJson);
            ?>
            <div class="postbox acf-postbox">
                <h2><strong><?= wp_get_theme(); ?></strong></h2>
                <div class="inside">
                    <div class="config-container">
                        <?= $configHTMLTree; ?>
                    </div>
                </div>
            </div>

            <?php
            foreach ($allThemes as $theme) :
                if ($theme->stylesheet === $currentTheme || $theme->stylesheet === "comeon-white") continue; // Skip current theme, and skip comeon-white
                if ( !file_exists(get_theme_root() . "/" . $theme->stylesheet . "/services/config.json") ) continue; // Skip current iteration if file not exists

                $configJson = file_get_contents(get_theme_root() . "/" . $theme->stylesheet . "/services/config.json");
                $configJson = json_decode($configJson, true);
                sort_alpha_r($configJson);
                $configHTMLTree = buildHtmlList($configJson);
            ?>
                <div class="postbox acf-postbox">
                    <h2><strong><?= $theme; ?></strong></h2>
                    <div class="inside">
                        <div class="config-container">
                            <?= $configHTMLTree; ?>
                        </div>
                    </div>
                </div>

            <?php endforeach; ?>
        </div>
    </div>
<?php
}