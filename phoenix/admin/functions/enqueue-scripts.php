<?php
// Admin area needed CSS/JS resources
function admin_resources() {
    global $post, $pagenow, $current_user;
    wp_enqueue_script('admin-script', get_template_directory_uri() . '/dist/admin.min.js', [], filemtime(get_template_directory() . '/dist/admin.min.js'), true);
    wp_enqueue_style('admin-style', get_stylesheet_directory_uri() . '/dist/admin.min.css', [], filemtime(get_template_directory() . '/dist/admin.min.css'));

    if (isFeatureActive('api/sportsbook')) {
        wp_localize_script('admin-script', 'matches_json', [json_encode(getMatches())]);
    }
    wp_localize_script('admin-script', 'icons', getIcons());

    $jackpotProviders = [];
    foreach (JACKPOT_PROVIDERS as $providerId => $provider) {
        if (isFeatureActive('api/jackpots/' . $providerId)) {
            $jackpotProviders[$providerId] = $provider;
            foreach ($provider['jackpots'] as $jackpotID => $jackpotName) {
                if(!function_exists('get_field')) continue;
                $jackpotNameTranslated = get_field('jackpot_games_jackpots_label_' . $jackpotID, 'option');
                $jackpotProviders[$providerId]['jackpots'][$jackpotID] = $jackpotNameTranslated ?: $jackpotName;
            }
        }
    }

    if(!function_exists('get_field')) {
         add_action('admin_notices', function () {
			$class          = 'notice notice-error';
			$notice_message = 'Jackpot Providers could not be loaded. Because get_field function does not exist. If you are sure ACF plugin is enabled, than an ACF update might caused this.';
			printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
		}
		);
    }

    wp_localize_script('admin-script', 'jackpot_providers', $jackpotProviders);

    // Check get parameter first, then transient, then fallback
    $simulated_time = $_GET['time_value'] ?? (get_transient('px_time_value_' . $current_user->ID) ?? getformatedDatetimeUtc(getNow()));

    // main_params for wp-admin
    wp_localize_script('admin-script', 'main_params', [
        'admin_url'             => admin_url(),
        'ajax_url'              => admin_url('admin-ajax.php'),
        'brand_url'             => brandUrl(),
        'login_url'             => loginUrl(),
        'loggedin'              => player()->isLoggedIn(),
        'locale'                => get_locale(),
        'brand'                 => CURRENT_BRAND,
        'currency'              => CURRENT_CURRENCY,
        'currency_symbol'       => CURRENT_CURRENCY_SYMBOL,
        'timezone'              => CURRENT_TIMEZONE,
        'region'                => CURRENT_REGION,
        'language'              => CURRENT_LANGUAGE,
        'country'               => CURRENT_COUNTRY,
        'tracking'              => TRACKING_PARAMS,
        'debug'                 => DEBUG_MODE,
        'dev'                   => DEV_ENV,
        'local'                 => LOCAL_ENV,
        'proxy'                 => PROXY_ENV,
        'site'                  => SITE_TYPE,
        'sportsbook'            => defined('SPORTSBOOK_PROVIDER') ? SPORTSBOOK_PROVIDER : '',
        'sportsbook_nonce'      => wp_create_nonce('sportsbook-nonce'),
        'simulated_time'        => $simulated_time,
        'wp_nonce'              => wp_create_nonce('acf_autofill_nonce'),
    ]);

    // Inject admin-post script only for editing and creating
    if ($pagenow === 'post.php' || $pagenow === 'post-new.php') {
        wp_enqueue_script('admin-post-script', get_template_directory_uri() . '/dist/admin-post.min.js', [], filemtime(get_template_directory() . '/dist/admin-post.min.js'), true);
    }
}

add_action('admin_enqueue_scripts', 'admin_resources');

// Custom ACF related resources
function acf_admin_resources() {
    $screen = get_current_screen();
    if($screen->base === 'post') {
        $path = "/admin/scripts/";
        wp_enqueue_script('acf-admin', get_template_directory_uri() . $path . 'acf-admin.js', false, filemtime(get_template_directory() . $path . 'acf-admin.js'), true);

        wp_enqueue_script('acf-advanced-validation-message', get_template_directory_uri() . $path . 'acf-advanced-validation-message.js', false, filemtime(get_template_directory() . $path . 'acf-advanced-validation-message.js'), true);

        // ACF Autofill for dev,local,staging only
        if(DEV_ENV || LOCAL_ENV || STAGING_ENV) {
            wp_enqueue_script('acf-autofill', get_template_directory_uri() . $path . 'acf-autofill.js', false, filemtime(get_template_directory() . $path . 'acf-autofill.js'), true);
        }
    }
}
add_action('acf/input/admin_enqueue_scripts', 'acf_admin_resources');

// Gutenberg template detection script
function enqueue_gutenberg_template_blocks_script() {
    $path = "/admin/scripts/";
    wp_enqueue_script(
        'gutenberg-template-blocks',
        get_template_directory_uri() . $path . 'gutenberg-template-blocks.js',
        array('wp-blocks', 'wp-data', 'wp-dom-ready', 'wp-hooks', 'jquery'),
        filemtime(get_template_directory() . $path . 'gutenberg-template-blocks.js'),
        true
    );

    // Localize script with nonce and ajaxurl
    wp_localize_script('gutenberg-template-blocks', 'phoenixTemplateBlocks', array(
        'nonce' => wp_create_nonce('phoenix_template_blocks_nonce'),
        'ajaxurl' => admin_url('admin-ajax.php')
    ));
}
add_action('enqueue_block_editor_assets', 'enqueue_gutenberg_template_blocks_script');

// iziToast library
// Source: https://izitoast.marcelodolza.com/
function izitoast_admin_notifications() {
    $screen = get_current_screen();

    if ( ! $screen || $screen->base !== 'post' ) {
        return;
    }
    $path = "/admin/scripts/vendor/izitoast/";
    wp_enqueue_script('izitoast-admin', get_template_directory_uri() . $path . 'iziToast.min.js', [], null, true);
    wp_enqueue_style('izitoast-admin-styles', get_template_directory_uri() . $path . 'iziToast.min.css', [], RELEASE_VERSION);
}
add_action('admin_enqueue_scripts', 'izitoast_admin_notifications');

function dequeue_non_critical_dashboard_scripts() {
    $screen = get_current_screen();

    if ( ! $screen || $screen->base !== 'dashboard' ) {
        return;
    }

    // List of critical scripts to keep loaded on dashboard home
    $home_screen_scripts = array(
        'jquery',            // jQuery is a core dependency for most admin pages
        // 'wp-util',           // Essential utility functions
        // 'heartbeat',         // Heartbeat API for auto-save, real-time updates
        // 'updates',           // Handles updates for WordPress
        // 'postbox',           // Toggles metaboxes
        // 'admin-bar',         // Admin bar at the top of the page
        'common',            // Common WordPress admin functions
        'dashboard',         // Core dashboard functionality
        'underscore',        // JavaScript library used by core
        'backbone',          // Backbone.js used by core,
        'site-health',
        'jquery-core',
        'wp-i18n',
        'query-monitor',
        'yoast-seo-admin-global',
        'yoast-seo-dashboard-widget',
        'yoast-seo-wincher-dashboard-widget',
        'admin-script', // Our own js script for admin enqueued with this name
    );
    global $wp_scripts;
    if ( $screen->base == 'dashboard' ) {
        // Loop through registered scripts in the admin area
        foreach ($wp_scripts->registered as $script) {
            if (!in_array($script->handle, $home_screen_scripts)) {
                wp_dequeue_script($script->handle);
            }
        }
    }
}
add_action( 'admin_enqueue_scripts', 'dequeue_non_critical_dashboard_scripts', 100 );