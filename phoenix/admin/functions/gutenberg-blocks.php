<?php
add_action('after_setup_theme', 'remove_default_patterns');
function remove_default_patterns() {
	remove_theme_support('core-block-patterns');
}

add_filter('allowed_block_types_all', 'filter_gutenberg_blocks', 25, 2);
function filter_gutenberg_blocks($allowedBlocks, $editorContext) {
	$registeredBlocks = array_keys(WP_Block_Type_Registry::get_instance()->get_all_registered());
	$allowedBlocks = [];

	define('ACF_BLOCK_SLUG', 'acf/');

	// Get page template from editor context or post meta
	$pageTemplate = '';
	if (isset($editorContext->post) && $editorContext->post->post_type == 'page') {
		$pageTemplate = get_post_meta($editorContext->post->ID, '_wp_page_template', true);

		// Debug logging (only in development)
		if (defined('WP_DEBUG') && WP_DEBUG) {
			error_log("Phoenix Debug: Post ID {$editorContext->post->ID}, Template: {$pageTemplate}");
		}
	}

	if (
		$editorContext->post->post_type == START_PAGE_SLUG ||
		$editorContext->post->post_type == DYNAMIC_SLUG ||
		$editorContext->post->post_type == SEO_PAGE_SLUG ||
		$pageTemplate === 'page-dynamic-blocks.php'
	) {
		// Allow Phoenix blocks and dynamic blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) ||
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG)
			) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if (
		$editorContext->post->post_type == RESPONSIBLE_GAMING_SLUG ||
		$pageTemplate === 'page-responsible-gaming.php'
	) {
		// Allow Phoenix blocks and responsible gaming blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if ($editorContext->post->post_type == AD_BANNER_SLUG) {
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . AD_BANNER_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	// For all other contexts, allow Phoenix blocks and non-dynamic blocks
	foreach ($registeredBlocks as $registeredBlock) {
		if (
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)
		) {
			$allowedBlocks[] = $registeredBlock;
		}
	}

	return $allowedBlocks;
}

add_filter('admin_body_class', 'add_pnp_body_classes'); // this should be removed after refactoring the pnp blocks
function add_pnp_body_classes($classes) {
	return "{$classes} pnp";
}

// Enhanced template detection for real-time block filtering
add_action('wp_ajax_phoenix_get_template_blocks', 'phoenix_get_template_blocks');
function phoenix_get_template_blocks() {
	// Verify nonce for security
	if (!wp_verify_nonce($_POST['nonce'], 'phoenix_template_blocks_nonce')) {
		wp_die('Security check failed');
	}

	$post_id = intval($_POST['post_id']);
	$template = sanitize_text_field($_POST['template']);

	// Update the template meta field
	if ($post_id && $template) {
		update_post_meta($post_id, '_wp_page_template', $template);
	}

	// Return success
	wp_send_json_success(array(
		'message' => 'Template updated',
		'template' => $template,
		'post_id' => $post_id
	));
}
