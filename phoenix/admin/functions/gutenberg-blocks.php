<?php
add_action('after_setup_theme', 'remove_default_patterns');
function remove_default_patterns() {
	remove_theme_support('core-block-patterns');
}

add_filter('allowed_block_types_all', 'filter_gutenberg_blocks', 25, 2);
function filter_gutenberg_blocks($allowedBlocks, $editorContext) {
	$registeredBlocks = array_keys(WP_Block_Type_Registry::get_instance()->get_all_registered());
	$allowedBlocks = [];

	define('ACF_BLOCK_SLUG', 'acf/');

	// Get page template from editor context or post meta
	$pageTemplate = '';
	if (isset($editorContext->post) && $editorContext->post->post_type == 'page') {
		$pageTemplate = get_post_meta($editorContext->post->ID, '_wp_page_template', true);

		// Debug logging (only in development)
		if (defined('WP_DEBUG') && WP_DEBUG) {
			error_log("Phoenix Debug: Post ID {$editorContext->post->ID}, Template: {$pageTemplate}");
		}
	}

	if (
		$editorContext->post->post_type == START_PAGE_SLUG ||
		$editorContext->post->post_type == DYNAMIC_SLUG ||
		$editorContext->post->post_type == SEO_PAGE_SLUG ||
		$pageTemplate === 'page-dynamic-blocks.php'
	) {
		// Allow Phoenix blocks and dynamic blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) ||
				str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG)
			) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if (
		$editorContext->post->post_type == RESPONSIBLE_GAMING_SLUG ||
		$pageTemplate === 'page-responsible-gaming.php'
	) {
		// Allow Phoenix blocks and responsible gaming blocks
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	if ($editorContext->post->post_type == AD_BANNER_SLUG) {
		foreach ($registeredBlocks as $registeredBlock) {
			if (str_contains($registeredBlock, ACF_BLOCK_SLUG . AD_BANNER_BLOCK_SLUG)) {
				$allowedBlocks[] = $registeredBlock;
			}
		}
		return $allowedBlocks;
	}

	// For all other contexts, allow Phoenix blocks and non-dynamic blocks
	foreach ($registeredBlocks as $registeredBlock) {
		if (
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . START_PAGE_DYNAMIC_PNP_BLOCK_SLUG) &&
			!str_contains($registeredBlock, ACF_BLOCK_SLUG . RESPONSIBLE_GAMING_BLOCK_SLUG)
		) {
			$allowedBlocks[] = $registeredBlock;
		}
	}

	return $allowedBlocks;
}

add_filter('admin_body_class', 'add_pnp_body_classes'); // this should be removed after refactoring the pnp blocks
function add_pnp_body_classes($classes) {
	return "{$classes} pnp";
}

// Enhanced template detection for real-time block filtering
add_action('wp_ajax_phoenix_get_template_blocks', 'phoenix_get_template_blocks');
function phoenix_get_template_blocks() {
	// Debug logging
	if (defined('WP_DEBUG') && WP_DEBUG) {
		error_log('Phoenix Debug: AJAX phoenix_get_template_blocks called');
		error_log('Phoenix Debug: POST data: ' . print_r($_POST, true));
	}

	// Verify nonce for security
	if (!wp_verify_nonce($_POST['nonce'], 'phoenix_template_blocks_nonce')) {
		if (defined('WP_DEBUG') && WP_DEBUG) {
			error_log('Phoenix Debug: Nonce verification failed');
		}
		wp_die('Security check failed');
	}

	$post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
	$template = sanitize_text_field($_POST['template']);

	if (defined('WP_DEBUG') && WP_DEBUG) {
		error_log("Phoenix Debug: Processing post_id: {$post_id}, template: {$template}");
	}

	// Update the template meta field if post_id is provided
	if ($post_id && $template) {
		$result = update_post_meta($post_id, '_wp_page_template', $template);
		if (defined('WP_DEBUG') && WP_DEBUG) {
			error_log("Phoenix Debug: update_post_meta result: " . ($result ? 'success' : 'failed'));
		}
	}

	// Get available blocks for the template
	$available_blocks = phoenix_get_blocks_for_template($template);

	// Return success with block data
	wp_send_json_success(array(
		'message' => 'Template updated',
		'template' => $template,
		'post_id' => $post_id,
		'blocks' => $available_blocks
	));
}

/**
 * Get available blocks for a specific template
 */
function phoenix_get_blocks_for_template($template) {
	// Get all registered block types
	$all_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();
	$available_blocks = array();

	// Template-specific block filtering
	foreach ($all_blocks as $block_name => $block_type) {
		$should_include = true;

		// Filter blocks based on template
		if ($template === 'page-dynamic-blocks.php') {
			// For dynamic blocks template, include dynamic blocks
			if (strpos($block_name, 'acf/dynamic-') === 0 ||
				strpos($block_name, 'acf/start-page-') === 0) {
				$should_include = true;
			} else {
				// Also include core blocks
				$should_include = strpos($block_name, 'core/') === 0;
			}
		} elseif ($template === 'page-responsible-gaming.php') {
			// For responsible gaming template, include responsible gaming blocks
			if (strpos($block_name, 'acf/responsible-gaming-') === 0) {
				$should_include = true;
			} else {
				// Also include core blocks
				$should_include = strpos($block_name, 'core/') === 0;
			}
		} else {
			// For default template, exclude template-specific blocks
			if (strpos($block_name, 'acf/dynamic-') === 0 ||
				strpos($block_name, 'acf/start-page-') === 0 ||
				strpos($block_name, 'acf/responsible-gaming-') === 0) {
				$should_include = false;
			}
		}

		if ($should_include) {
			$available_blocks[] = array(
				'name' => $block_name,
				'title' => $block_type->title,
				'category' => $block_type->category,
				'icon' => $block_type->icon
			);
		}
	}

	if (defined('WP_DEBUG') && WP_DEBUG) {
		error_log("Phoenix Debug: Found " . count($available_blocks) . " blocks for template: {$template}");
	}

	return $available_blocks;
}
