/* global wp, jQuery */

/**
 * Real-time template detection for Gutenberg blocks
 * Detects when a user selects a page template and refreshes available blocks
 */
(function ($) {
    'use strict';

    // Only run in Gutenberg editor
    if (typeof wp === 'undefined' || !wp.data || !wp.blocks) {
        return;
    }

    let currentTemplate = '';
    let isInitialized = false;

    /**
     * Initialize template detection
     */
    function initTemplateDetection() {
        if (isInitialized) {
            return;
        }

        // Wait for editor to be ready
        wp.domReady(function () {
            // Check if we're editing a page
            const postType = wp.data.select('core/editor')?.getCurrentPostType();
            if (postType !== 'page') {
                return;
            }

            isInitialized = true;

            // Get initial template
            currentTemplate = getCurrentTemplate();

            // Subscribe to template changes
            wp.data.subscribe(handleTemplateChange);

            console.log('Phoenix: Template detection initialized');
        });
    }

    /**
     * Get current selected template
     */
    function getCurrentTemplate() {
        const template = wp.data.select('core/editor')?.getEditedPostAttribute('template');
        return template || '';
    }

    /**
     * Handle template changes
     */
    function handleTemplateChange() {
        const newTemplate = getCurrentTemplate();

        if (newTemplate !== currentTemplate) {
            console.log('Phoenix: Template changed from', currentTemplate, 'to', newTemplate);
            currentTemplate = newTemplate;

            // Update template meta via AJAX and refresh blocks
            updateTemplateAndRefreshBlocks(newTemplate);
        }
    }

    /**
     * Update template meta and refresh blocks
     */
    function updateTemplateAndRefreshBlocks(template) {
        const postId = wp.data.select('core/editor')?.getCurrentPostId();

        if (!postId) {
            console.log('Phoenix: No post ID found, skipping AJAX update');
            refreshAvailableBlocks(template);
            return;
        }

        // Update template meta via AJAX
        jQuery.post(phoenixTemplateBlocks.ajaxurl, {
            action: 'phoenix_get_template_blocks',
            post_id: postId,
            template: template,
            nonce: phoenixTemplateBlocks.nonce
        }, function(response) {
            if (response.success) {
                console.log('Phoenix: Template meta updated successfully');
                refreshAvailableBlocks(template);
            } else {
                console.log('Phoenix: Failed to update template meta, refreshing anyway');
                refreshAvailableBlocks(template);
            }
        }).fail(function() {
            console.log('Phoenix: AJAX request failed, refreshing anyway');
            refreshAvailableBlocks(template);
        });
    }

    /**
     * Refresh available blocks based on template
     */
    function refreshAvailableBlocks(template) {
        console.log('Phoenix: Refreshing blocks for template:', template);

        // Use WordPress data API to trigger a refresh
        setTimeout(function() {
            // Method 1: Trigger editor refresh
            if (wp.data && wp.data.dispatch) {
                const editorDispatch = wp.data.dispatch('core/editor');
                if (editorDispatch && editorDispatch.refreshPost) {
                    editorDispatch.refreshPost();
                }
            }

            // Method 2: Force block inserter refresh
            const blockInserter = document.querySelector('.block-editor-inserter__menu');
            if (blockInserter) {
                blockInserter.style.display = 'none';
                setTimeout(function() {
                    blockInserter.style.display = '';
                }, 10);
            }

            // Method 3: Trigger block registry refresh
            if (wp.hooks && wp.hooks.doAction) {
                wp.hooks.doAction('blocks.registerBlockType');
            }

            // Method 4: Force page reload as last resort (commented out for now)
            // window.location.reload();

            console.log('Phoenix: Block refresh completed');
        }, 200);
    }

    /**
     * Alternative method: Listen for template dropdown changes
     */
    function initTemplateDropdownListener() {
        // Wait for the template dropdown to be available
        const checkForDropdown = setInterval(function() {
            const templateSelect = document.querySelector('select[name="page_template"]');
            if (templateSelect) {
                clearInterval(checkForDropdown);

                templateSelect.addEventListener('change', function(e) {
                    const selectedTemplate = e.target.value;
                    console.log('Phoenix: Template dropdown changed to', selectedTemplate);

                    // Update the current template and refresh blocks
                    currentTemplate = selectedTemplate;
                    refreshAvailableBlocks(selectedTemplate);
                });

                console.log('Phoenix: Template dropdown listener initialized');
            }
        }, 500);

        // Stop checking after 10 seconds
        setTimeout(function() {
            clearInterval(checkForDropdown);
        }, 10000);
    }

    /**
     * Enhanced block refresh using WordPress APIs
     */
    function enhancedBlockRefresh() {
        // Force re-evaluation of allowed block types
        if (wp.hooks && wp.hooks.doAction) {
            wp.hooks.doAction('blocks.allowedBlockTypes.refresh');
        }

        // Dispatch editor refresh
        if (wp.data && wp.data.dispatch) {
            const editorDispatch = wp.data.dispatch('core/editor');
            const blockEditorDispatch = wp.data.dispatch('core/block-editor');

            if (editorDispatch && editorDispatch.refreshPost) {
                editorDispatch.refreshPost();
            }

            if (blockEditorDispatch && blockEditorDispatch.resetBlocks) {
                // Get current blocks and reset them to trigger refresh
                const blocks = wp.data.select('core/block-editor').getBlocks();
                blockEditorDispatch.resetBlocks(blocks);
            }
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        initTemplateDetection();
        initTemplateDropdownListener();
    });

    // Also initialize when WordPress is ready
    if (wp.domReady) {
        wp.domReady(initTemplateDetection);
    }

})(jQuery);
