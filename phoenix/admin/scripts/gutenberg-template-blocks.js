/* global wp, jQuery, phoenixTemplateBlocks */

/**
 * Real-time template detection for Gutenberg blocks
 * Detects when a user selects a page template and refreshes available blocks
 */
(function ($) {
    'use strict';

    console.log('Phoenix: gutenberg-template-blocks.js loaded!');
    console.log('Phoenix: phoenixTemplateBlocks object:', typeof phoenixTemplateBlocks, phoenixTemplateBlocks);
    console.log('Phoenix: Current URL:', window.location.href);
    console.log('Phoenix: Is Gutenberg editor?', document.body.classList.contains('block-editor-page'));

    // Only run in Gutenberg editor
    console.log('Phoenix: Script loaded, checking WordPress APIs...');
    console.log('Phoenix: wp object:', typeof wp);
    console.log('Phoenix: wp.data:', typeof wp?.data);
    console.log('Phoenix: wp.blocks:', typeof wp?.blocks);
    console.log('Phoenix: wp.domReady:', typeof wp?.domReady);

    if (typeof wp === 'undefined' || !wp.data || !wp.blocks) {
        console.log('Phoenix: WordPress APIs not available, exiting');
        return;
    }

    console.log('Phoenix: WordPress APIs available, continuing...');

    let currentTemplate = '';
    let isInitialized = false;

    /**
     * Initialize template detection
     */
    function initTemplateDetection() {
        console.log('Phoenix: initTemplateDetection called, isInitialized:', isInitialized);

        if (isInitialized) {
            console.log('Phoenix: Already initialized, returning');
            return;
        }

        // Wait for editor to be ready
        wp.domReady(function () {
            console.log('Phoenix: wp.domReady fired');

            // Check if we're editing a page (with fallback methods)
            let postType = wp.data.select('core/editor')?.getCurrentPostType();

            // Fallback: check URL for post_type parameter
            if (!postType) {
                const urlParams = new URLSearchParams(window.location.search);
                postType = urlParams.get('post_type') || 'page'; // default to page if not specified
                console.log('Phoenix: Post type from URL:', postType);
            }

            // Fallback: check if we're in post-new.php or post.php without post_type (which means page)
            if (!postType && (window.location.href.includes('post-new.php') || window.location.href.includes('post.php'))) {
                postType = 'page';
                console.log('Phoenix: Assuming page post type from URL pattern');
            }

            console.log('Phoenix: Final post type:', postType);

            // Initialize for pages or if we can't determine post type (better to try than skip)
            if (postType === 'page' || !postType) {
                console.log('Phoenix: Initializing template detection');

                isInitialized = true;

                // Get initial template
                currentTemplate = getCurrentTemplate();
                console.log('Phoenix: Initial template:', currentTemplate);

                // Subscribe to template changes
                wp.data.subscribe(handleTemplateChange);

                console.log('Phoenix: Template detection initialized successfully');
            } else {
                console.log('Phoenix: Not a page (' + postType + '), skipping template detection');
            }
        });
    }

    /**
     * Get current selected template
     */
    function getCurrentTemplate() {
        const template = wp.data.select('core/editor')?.getEditedPostAttribute('template');
        console.log('Phoenix: getCurrentTemplate called, raw template:', template);

        // Also try to get from meta
        const meta = wp.data.select('core/editor')?.getEditedPostAttribute('meta');
        console.log('Phoenix: Post meta:', meta);

        const finalTemplate = template || '';
        console.log('Phoenix: Final template value:', finalTemplate);
        return finalTemplate;
    }

    /**
     * Handle template changes
     */
    function handleTemplateChange() {
        const newTemplate = getCurrentTemplate();
        console.log('Phoenix: handleTemplateChange called, current:', currentTemplate, 'new:', newTemplate);

        if (newTemplate !== currentTemplate) {
            console.log('Phoenix: Template changed from', currentTemplate, 'to', newTemplate);
            currentTemplate = newTemplate;

            // Update template meta via AJAX and refresh blocks
            updateTemplateAndRefreshBlocks(newTemplate);
        } else {
            // Uncomment this for very verbose logging
            // console.log('Phoenix: No template change detected');
        }
    }

    /**
     * Update template meta and refresh blocks
     */
    function updateTemplateAndRefreshBlocks(template) {
        const postId = wp.data.select('core/editor')?.getCurrentPostId();

        if (!postId) {
            console.log('Phoenix: No post ID found, skipping AJAX update');
            refreshAvailableBlocks(template);
            return;
        }

        // Update template meta via AJAX
        jQuery.post(phoenixTemplateBlocks.ajaxurl, {
            action: 'phoenix_get_template_blocks',
            post_id: postId,
            template: template,
            nonce: phoenixTemplateBlocks.nonce
        }, function(response) {
            if (response.success) {
                console.log('Phoenix: Template meta updated successfully');
                refreshAvailableBlocks(template);
            } else {
                console.log('Phoenix: Failed to update template meta, refreshing anyway');
                refreshAvailableBlocks(template);
            }
        }).fail(function() {
            console.log('Phoenix: AJAX request failed, refreshing anyway');
            refreshAvailableBlocks(template);
        });
    }

    /**
     * Refresh available blocks based on template
     */
    function refreshAvailableBlocks(template) {
        console.log('Phoenix: Refreshing blocks for template:', template);

        // Use WordPress data API to trigger a refresh
        setTimeout(function() {
            // Method 1: Force reload the page (most reliable but disruptive)
            if (template === 'page-dynamic-blocks.php') {
                console.log('Phoenix: Dynamic blocks template detected, reloading page...');
                window.location.reload();
                return;
            }

            // Method 2: Try to refresh editor state
            if (wp.data && wp.data.dispatch) {
                const editorDispatch = wp.data.dispatch('core/editor');
                const blockEditorDispatch = wp.data.dispatch('core/block-editor');

                if (editorDispatch && editorDispatch.refreshPost) {
                    console.log('Phoenix: Refreshing post via editor dispatch');
                    editorDispatch.refreshPost();
                }

                // Try to reset blocks to trigger re-evaluation
                if (blockEditorDispatch && blockEditorDispatch.resetBlocks) {
                    const blocks = wp.data.select('core/block-editor')?.getBlocks() || [];
                    console.log('Phoenix: Resetting blocks, count:', blocks.length);
                    blockEditorDispatch.resetBlocks(blocks);
                }
            }

            // Method 3: Force block inserter refresh
            const blockInserter = document.querySelector('.block-editor-inserter__menu');
            if (blockInserter) {
                console.log('Phoenix: Refreshing block inserter');
                blockInserter.style.display = 'none';
                setTimeout(function() {
                    blockInserter.style.display = '';
                }, 10);
            }

            // Method 4: Trigger block registry refresh
            if (wp.hooks && wp.hooks.doAction) {
                console.log('Phoenix: Triggering block registry hooks');
                wp.hooks.doAction('blocks.registerBlockType');
            }

            console.log('Phoenix: Block refresh completed');
        }, 500); // Increased delay to ensure template meta is updated
    }

    /**
     * Alternative method: Listen for template dropdown changes
     */
    function initTemplateDropdownListener() {
        console.log('Phoenix: initTemplateDropdownListener called');

        // Wait for the template dropdown to be available
        const checkForDropdown = setInterval(function() {
            const templateSelect = document.querySelector('select[name="page_template"]');
            console.log('Phoenix: Checking for template dropdown, found:', !!templateSelect);

            if (templateSelect) {
                clearInterval(checkForDropdown);
                console.log('Phoenix: Template dropdown found:', templateSelect);

                templateSelect.addEventListener('change', function(e) {
                    const selectedTemplate = e.target.value;
                    console.log('Phoenix: Template dropdown changed to', selectedTemplate);

                    // Update the current template and refresh blocks
                    currentTemplate = selectedTemplate;
                    refreshAvailableBlocks(selectedTemplate);
                });

                console.log('Phoenix: Template dropdown listener initialized');
            }
        }, 500);

        // Stop checking after 10 seconds
        setTimeout(function() {
            console.log('Phoenix: Stopping template dropdown search after 10 seconds');
            clearInterval(checkForDropdown);
        }, 10000);
    }

    /**
     * Alternative method: Watch for template selector changes using MutationObserver
     */
    function initTemplateSelectorObserver() {
        console.log('Phoenix: initTemplateSelectorObserver called');

        // Watch for changes in the document
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Look for template selector changes
                if (mutation.type === 'childList') {
                    const templateSelectors = document.querySelectorAll('[data-label="Template"], .editor-page-attributes__template select, .components-select-control__input');
                    templateSelectors.forEach(function(selector) {
                        if (selector.tagName === 'SELECT' && !selector.hasAttribute('data-phoenix-listener')) {
                            console.log('Phoenix: Found template selector, adding listener:', selector);
                            selector.setAttribute('data-phoenix-listener', 'true');
                            selector.addEventListener('change', function(e) {
                                console.log('Phoenix: Template selector changed:', e.target.value);
                                currentTemplate = e.target.value;
                                refreshAvailableBlocks(e.target.value);
                            });
                        }
                    });
                }
            });
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('Phoenix: MutationObserver started');
    }



    // Initialize when DOM is ready
    $(document).ready(function() {
        console.log('Phoenix: jQuery document ready fired');
        initTemplateDetection();
        initTemplateDropdownListener();
        initTemplateSelectorObserver();
    });

    // Also initialize when WordPress is ready
    if (wp.domReady) {
        console.log('Phoenix: wp.domReady available, setting up callback');
        wp.domReady(function() {
            console.log('Phoenix: wp.domReady callback fired');
            initTemplateDetection();
        });
    } else {
        console.log('Phoenix: wp.domReady not available');
    }

    console.log('Phoenix: Script initialization complete');

})(jQuery);
