/* global wp, jQuery, phoenixTemplateBlocks */

/**
 * Real-time template detection for Gutenberg blocks
 * Detects when a user selects a page template and refreshes available blocks
 */
(function ($) {
    'use strict';

    console.log('Phoenix: gutenberg-template-blocks.js loaded!');
    console.log('Phoenix: phoenixTemplateBlocks object:', typeof phoenixTemplateBlocks, phoenixTemplateBlocks);
    console.log('Phoenix: Current URL:', window.location.href);
    console.log('Phoenix: Is Gutenberg editor?', document.body.classList.contains('block-editor-page'));

    // Only run in Gutenberg editor
    console.log('Phoenix: Script loaded, checking WordPress APIs...');
    console.log('Phoenix: wp object:', typeof wp);
    console.log('Phoenix: wp.data:', typeof wp?.data);
    console.log('Phoenix: wp.blocks:', typeof wp?.blocks);
    console.log('Phoenix: wp.domReady:', typeof wp?.domReady);

    if (typeof wp === 'undefined' || !wp.data || !wp.blocks) {
        console.log('Phoenix: WordPress APIs not available, exiting');
        return;
    }

    console.log('Phoenix: WordPress APIs available, continuing...');

    let currentTemplate = '';
    let isInitialized = false;
    let lastSubscriberRun = 0;
    let subscriberThrottle = 100; // Minimum 100ms between subscriber runs

    /**
     * Initialize template detection
     */
    function initTemplateDetection() {
        console.log('Phoenix: initTemplateDetection called, isInitialized:', isInitialized);

        if (isInitialized) {
            console.log('Phoenix: Already initialized, returning');
            return;
        }

        // Wait for editor to be ready
        wp.domReady(function () {
            console.log('Phoenix: wp.domReady fired');

            // Check if we're editing a page (with fallback methods)
            let postType = wp.data.select('core/editor')?.getCurrentPostType();

            // Fallback: check URL for post_type parameter
            if (!postType) {
                const urlParams = new URLSearchParams(window.location.search);
                postType = urlParams.get('post_type') || 'page'; // default to page if not specified
                console.log('Phoenix: Post type from URL:', postType);
            }

            // Fallback: check if we're in post-new.php or post.php without post_type (which means page)
            if (!postType && (window.location.href.includes('post-new.php') || window.location.href.includes('post.php'))) {
                postType = 'page';
                console.log('Phoenix: Assuming page post type from URL pattern');
            }

            console.log('Phoenix: Final post type:', postType);

            // Initialize for pages or if we can't determine post type (better to try than skip)
            if (postType === 'page' || !postType) {
                console.log('Phoenix: Initializing template detection');

                // Get initial template first
                currentTemplate = getCurrentTemplate();
                console.log('Phoenix: Initial template:', currentTemplate);

                // Set initialized flag
                isInitialized = true;

                // Subscribe to template changes
                wp.data.subscribe(handleTemplateChange);

                console.log('Phoenix: Template detection initialized successfully');
            } else {
                console.log('Phoenix: Not a page (' + postType + '), skipping template detection');
            }
        });
    }

    /**
     * Get current selected template
     */
    function getCurrentTemplate() {
        // For new pages, start with 'default' template
        if (window.location.href.includes('post-new.php')) {
            // Check if user has selected a template via WordPress editor
            const editorTemplate = wp.data.select('core/editor')?.getEditedPostAttribute('template');
            return editorTemplate || 'default';
        }

        // For existing pages, try to get from post meta or editor
        const editorTemplate = wp.data.select('core/editor')?.getEditedPostAttribute('template');
        if (editorTemplate) {
            return editorTemplate;
        }

        const postMeta = wp.data.select('core/editor')?.getEditedPostAttribute('meta') || {};
        return postMeta._wp_page_template || 'default';
    }

    /**
     * Handle template changes
     */
    function handleTemplateChange() {
        if (!isInitialized) return;

        // Throttle to prevent infinite loops
        const now = Date.now();
        if (now - lastSubscriberRun < subscriberThrottle) {
            return;
        }
        lastSubscriberRun = now;

        const newTemplate = getCurrentTemplate();

        // Only log if there's an actual change to reduce spam
        if (newTemplate !== currentTemplate) {
            console.log('Phoenix: Template changed from', currentTemplate, 'to', newTemplate);
            currentTemplate = newTemplate;

            // Update template meta via AJAX and refresh blocks
            updateTemplateAndRefreshBlocks(newTemplate);
        }
    }

    /**
     * Update template meta and refresh blocks
     */
    function updateTemplateAndRefreshBlocks(template) {
        const postId = wp.data.select('core/editor')?.getCurrentPostId();

        if (!postId) {
            console.log('Phoenix: No post ID found, skipping AJAX update');
            refreshAvailableBlocks(template);
            return;
        }

        // Update template meta via AJAX
        jQuery.post(phoenixTemplateBlocks.ajaxurl, {
            action: 'phoenix_get_template_blocks',
            post_id: postId,
            template: template,
            nonce: phoenixTemplateBlocks.nonce
        }, function(response) {
            if (response.success) {
                console.log('Phoenix: Template meta updated successfully');
                refreshAvailableBlocks(template);
            } else {
                console.log('Phoenix: Failed to update template meta, refreshing anyway');
                refreshAvailableBlocks(template);
            }
        }).fail(function() {
            console.log('Phoenix: AJAX request failed, refreshing anyway');
            refreshAvailableBlocks(template);
        });
    }

    /**
     * Refresh available blocks based on template
     */
    function refreshAvailableBlocks(template) {
        console.log('Phoenix: Refreshing blocks for template:', template);

        // Fetch new block data for the template via AJAX
        jQuery.post(phoenixTemplateBlocks.ajaxurl, {
            action: 'phoenix_get_template_blocks',
            template: template,
            nonce: phoenixTemplateBlocks.nonce
        }, function(response) {
            console.log('Phoenix: AJAX response received:', response);

            if (response.success && response.data) {
                console.log('Phoenix: Received new block data for template:', template);
                console.log('Phoenix: Block data:', response.data.blocks);
                console.log('Phoenix: Number of blocks:', response.data.blocks ? response.data.blocks.length : 0);

                // Update the global phoenixTemplateBlocks with new data
                if (response.data.blocks) {
                    phoenixTemplateBlocks.blocks = response.data.blocks;
                    console.log('Phoenix: Updated phoenixTemplateBlocks.blocks:', phoenixTemplateBlocks.blocks);
                }

                // Debug: Check current WordPress block registry
                if (wp.blocks && wp.blocks.getBlockTypes) {
                    const currentBlocks = wp.blocks.getBlockTypes();
                    console.log('Phoenix: Current registered blocks:', currentBlocks.length);
                    console.log('Phoenix: Current block names:', currentBlocks.map(b => b.name));
                }

                // Update editor settings to filter blocks based on template
                updateEditorBlockSettings(response.data.blocks, template);

                // Refresh the block inserter to show new blocks
                refreshBlockInserter();

                // Trigger WordPress hooks to notify other plugins/themes
                if (wp.hooks && wp.hooks.doAction) {
                    wp.hooks.doAction('phoenix.template.changed', template, response.data);
                }

                console.log('Phoenix: Block refresh completed for template:', template);
            } else {
                console.log('Phoenix: Failed to get block data for template:', template);
                console.log('Phoenix: Response:', response);
            }
        }).fail(function() {
            console.log('Phoenix: AJAX request failed for template blocks');
        });
    }

    /**
     * Update editor settings to control which blocks are available
     */
    function updateEditorBlockSettings(availableBlocks, template) {
        console.log('Phoenix: Updating editor block settings for template:', template);

        if (!wp.data || !wp.data.dispatch || !wp.data.select) {
            console.log('Phoenix: WordPress data API not available');
            return;
        }

        const editorDispatch = wp.data.dispatch('core/editor');
        const editorSelect = wp.data.select('core/editor');

        if (!editorDispatch || !editorSelect) {
            console.log('Phoenix: Editor dispatch/select not available');
            return;
        }

        // Get current editor settings
        const currentSettings = editorSelect.getEditorSettings() || {};
        console.log('Phoenix: Current editor settings:', currentSettings);

        // Create allowed blocks list from the available blocks
        const allowedBlocks = availableBlocks.map(block => block.name);
        console.log('Phoenix: Setting allowed blocks:', allowedBlocks);

        // Update editor settings with new allowed blocks
        const newSettings = {
            ...currentSettings,
            allowedBlockTypes: allowedBlocks,
            __phoenixTemplateRefresh: Date.now() // Force refresh
        };

        console.log('Phoenix: Updating editor settings with new allowed blocks');
        editorDispatch.updateEditorSettings(newSettings);

        // Also try to update the block editor settings if available
        const blockEditorDispatch = wp.data.dispatch('core/block-editor');
        if (blockEditorDispatch && blockEditorDispatch.updateSettings) {
            console.log('Phoenix: Also updating block editor settings');
            blockEditorDispatch.updateSettings({
                allowedBlockTypes: allowedBlocks
            });
        }
    }

    /**
     * Refresh the block inserter without reloading the page
     */
    function refreshBlockInserter() {
        console.log('Phoenix: Refreshing block inserter');

        // Method 1: Close and reopen the block inserter if it's open to force refresh
        const inserterButton = document.querySelector('.edit-post-header-toolbar__inserter-toggle, .editor-document-tools__inserter-toggle');
        if (inserterButton) {
            const isPressed = inserterButton.getAttribute('aria-pressed') === 'true';
            if (isPressed) {
                console.log('Phoenix: Closing and reopening block inserter to refresh available blocks');
                inserterButton.click(); // Close
                setTimeout(() => {
                    inserterButton.click(); // Reopen with new block settings
                }, 300);
                return; // Exit early since we're refreshing the inserter
            }
        }

        // Method 2: If inserter is not open, trigger a general refresh
        console.log('Phoenix: Block inserter not open, triggering general refresh');

        // Trigger WordPress hooks to notify about block changes
        if (wp.hooks && wp.hooks.doAction) {
            console.log('Phoenix: Triggering block refresh hooks');
            wp.hooks.doAction('phoenix.blocks.refreshed');
        }

        // Method 3: Force refresh of the block editor interface
        if (wp.data && wp.data.dispatch) {
            const blockEditorDispatch = wp.data.dispatch('core/block-editor');
            if (blockEditorDispatch) {
                console.log('Phoenix: Triggering block editor refresh');
                // This can help refresh the block editor interface
                setTimeout(() => {
                    if (wp.hooks) {
                        wp.hooks.doAction('blocks.registerBlockType');
                    }
                }, 100);
            }
        }
    }







    // Initialize when DOM is ready
    $(document).ready(function() {
        console.log('Phoenix: jQuery document ready fired');
        initTemplateDetection();
    });

    // Also initialize when WordPress is ready
    if (wp.domReady) {
        console.log('Phoenix: wp.domReady available, setting up callback');
        wp.domReady(function() {
            console.log('Phoenix: wp.domReady callback fired');
            initTemplateDetection();
        });
    } else {
        console.log('Phoenix: wp.domReady not available');
    }

    console.log('Phoenix: Script initialization complete');

})(jQuery);
