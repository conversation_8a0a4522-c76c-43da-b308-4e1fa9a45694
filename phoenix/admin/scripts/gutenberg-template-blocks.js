/* global wp, jQuery, phoenixTemplateBlocks */

/**
 * Real-time template detection for Gutenberg blocks
 * Detects when a user selects a page template and refreshes available blocks
 */
(function ($) {
    'use strict';

    console.log('Phoenix: gutenberg-template-blocks.js loaded!');
    console.log('Phoenix: phoenixTemplateBlocks object:', typeof phoenixTemplateBlocks, phoenixTemplateBlocks);
    console.log('Phoenix: Current URL:', window.location.href);
    console.log('Phoenix: Is Gutenberg editor?', document.body.classList.contains('block-editor-page'));

    // Only run in Gutenberg editor
    console.log('Phoenix: Script loaded, checking WordPress APIs...');
    console.log('Phoenix: wp object:', typeof wp);
    console.log('Phoenix: wp.data:', typeof wp?.data);
    console.log('Phoenix: wp.blocks:', typeof wp?.blocks);
    console.log('Phoenix: wp.domReady:', typeof wp?.domReady);

    if (typeof wp === 'undefined' || !wp.data || !wp.blocks) {
        console.log('Phoenix: WordPress APIs not available, exiting');
        return;
    }

    console.log('Phoenix: WordPress APIs available, continuing...');

    let currentTemplate = '';
    let isInitialized = false;
    let lastSubscriberRun = 0;
    let subscriberThrottle = 100; // Minimum 100ms between subscriber runs

    /**
     * Initialize template detection
     */
    function initTemplateDetection() {
        console.log('Phoenix: initTemplateDetection called, isInitialized:', isInitialized);

        if (isInitialized) {
            console.log('Phoenix: Already initialized, returning');
            return;
        }

        // Wait for editor to be ready
        wp.domReady(function () {
            console.log('Phoenix: wp.domReady fired');

            // Check if we're editing a page (with fallback methods)
            let postType = wp.data.select('core/editor')?.getCurrentPostType();

            // Fallback: check URL for post_type parameter
            if (!postType) {
                const urlParams = new URLSearchParams(window.location.search);
                postType = urlParams.get('post_type') || 'page'; // default to page if not specified
                console.log('Phoenix: Post type from URL:', postType);
            }

            // Fallback: check if we're in post-new.php or post.php without post_type (which means page)
            if (!postType && (window.location.href.includes('post-new.php') || window.location.href.includes('post.php'))) {
                postType = 'page';
                console.log('Phoenix: Assuming page post type from URL pattern');
            }

            console.log('Phoenix: Final post type:', postType);

            // Initialize for pages or if we can't determine post type (better to try than skip)
            if (postType === 'page' || !postType) {
                console.log('Phoenix: Initializing template detection');

                // Get initial template first
                currentTemplate = getCurrentTemplate();
                console.log('Phoenix: Initial template:', currentTemplate);

                // Set initialized flag
                isInitialized = true;

                // Subscribe to template changes
                wp.data.subscribe(handleTemplateChange);

                console.log('Phoenix: Template detection initialized successfully');
            } else {
                console.log('Phoenix: Not a page (' + postType + '), skipping template detection');
            }
        });
    }

    /**
     * Get current selected template
     */
    function getCurrentTemplate() {
        // For new pages, start with 'default' template
        if (window.location.href.includes('post-new.php')) {
            // Check if user has selected a template via WordPress editor
            const editorTemplate = wp.data.select('core/editor')?.getEditedPostAttribute('template');
            return editorTemplate || 'default';
        }

        // For existing pages, try to get from post meta or editor
        const editorTemplate = wp.data.select('core/editor')?.getEditedPostAttribute('template');
        if (editorTemplate) {
            return editorTemplate;
        }

        const postMeta = wp.data.select('core/editor')?.getEditedPostAttribute('meta') || {};
        return postMeta._wp_page_template || 'default';
    }

    /**
     * Handle template changes
     */
    function handleTemplateChange() {
        if (!isInitialized) return;

        // Throttle to prevent infinite loops
        const now = Date.now();
        if (now - lastSubscriberRun < subscriberThrottle) {
            return;
        }
        lastSubscriberRun = now;

        const newTemplate = getCurrentTemplate();

        // Only log if there's an actual change to reduce spam
        if (newTemplate !== currentTemplate) {
            console.log('Phoenix: Template changed from', currentTemplate, 'to', newTemplate);
            currentTemplate = newTemplate;

            // Update template meta via AJAX and refresh blocks
            updateTemplateAndRefreshBlocks(newTemplate);
        }
    }

    /**
     * Update template meta and refresh blocks
     */
    function updateTemplateAndRefreshBlocks(template) {
        const postId = wp.data.select('core/editor')?.getCurrentPostId();

        if (!postId) {
            console.log('Phoenix: No post ID found, skipping AJAX update');
            refreshAvailableBlocks(template);
            return;
        }

        // Update template meta via AJAX
        jQuery.post(phoenixTemplateBlocks.ajaxurl, {
            action: 'phoenix_get_template_blocks',
            post_id: postId,
            template: template,
            nonce: phoenixTemplateBlocks.nonce
        }, function(response) {
            if (response.success) {
                console.log('Phoenix: Template meta updated successfully');
                refreshAvailableBlocks(template);
            } else {
                console.log('Phoenix: Failed to update template meta, refreshing anyway');
                refreshAvailableBlocks(template);
            }
        }).fail(function() {
            console.log('Phoenix: AJAX request failed, refreshing anyway');
            refreshAvailableBlocks(template);
        });
    }

    /**
     * Refresh available blocks based on template
     */
    function refreshAvailableBlocks(template) {
        console.log('Phoenix: Refreshing blocks for template:', template);

        // Use WordPress data API to trigger a refresh
        setTimeout(function() {
            // Method 1: Force reload the page (most reliable but disruptive)
            if (template === 'page-dynamic-blocks.php') {
                console.log('Phoenix: Dynamic blocks template detected, reloading page...');
                window.location.reload();
                return;
            }

            // Method 2: Try to refresh editor state
            if (wp.data && wp.data.dispatch) {
                const editorDispatch = wp.data.dispatch('core/editor');
                const blockEditorDispatch = wp.data.dispatch('core/block-editor');

                if (editorDispatch && editorDispatch.refreshPost) {
                    console.log('Phoenix: Refreshing post via editor dispatch');
                    editorDispatch.refreshPost();
                }

                // Try to reset blocks to trigger re-evaluation
                if (blockEditorDispatch && blockEditorDispatch.resetBlocks) {
                    const blocks = wp.data.select('core/block-editor')?.getBlocks() || [];
                    console.log('Phoenix: Resetting blocks, count:', blocks.length);
                    blockEditorDispatch.resetBlocks(blocks);
                }
            }

            // Method 3: Force block inserter refresh
            const blockInserter = document.querySelector('.block-editor-inserter__menu');
            if (blockInserter) {
                console.log('Phoenix: Refreshing block inserter');
                blockInserter.style.display = 'none';
                setTimeout(function() {
                    blockInserter.style.display = '';
                }, 10);
            }

            // Method 4: Trigger block registry refresh
            if (wp.hooks && wp.hooks.doAction) {
                console.log('Phoenix: Triggering block registry hooks');
                wp.hooks.doAction('blocks.registerBlockType');
            }

            console.log('Phoenix: Block refresh completed');
        }, 500); // Increased delay to ensure template meta is updated
    }







    // Initialize when DOM is ready
    $(document).ready(function() {
        console.log('Phoenix: jQuery document ready fired');
        initTemplateDetection();
    });

    // Also initialize when WordPress is ready
    if (wp.domReady) {
        console.log('Phoenix: wp.domReady available, setting up callback');
        wp.domReady(function() {
            console.log('Phoenix: wp.domReady callback fired');
            initTemplateDetection();
        });
    } else {
        console.log('Phoenix: wp.domReady not available');
    }

    console.log('Phoenix: Script initialization complete');

})(jQuery);
