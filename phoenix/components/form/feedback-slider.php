<?php
if (!is_array($args)) $args = []; // Fallback for array_merge below

$args = array_merge([
    'name' => '',
    'class' => '',
    'id' => '',
    'value' => '',
    'lower_label' => '',
    'higher_label' => ''
], $args);
?>
<div class="feedback-slider <?= $args['class']; ?>">
    <div class="feedback-slider__range">
        <input
            class="feedback-slider__input js-feeback-slider-input"
            <?php if (!empty($args['id'])) : ?>id="<?= $args['id']; ?>" <?php endif; ?>
            <?php if (!empty($args['name'])) : ?>name="<?= $args['name']; ?>" <?php endif; ?>
            value="<?= $args['value']; ?>"
            type="range" min="1" max="5" value="3">
        <div class="feedback-slider__track"></div>
        <div class="feedback-slider__bar"></div>
        <?php for ($i = 1; $i <= 5; $i++) : ?>
            <div class="feedback-slider__mark feedback-slider__mark--<?= $i; ?>"></div>
        <?php endfor; ?>
    </div>

    <?php if (!empty($args['lower_label']) || !empty($args['higher_label'])) : ?>
        <div class="feedback-slider__labels">
            <div class="feedback-slider__label"><?= $args['lower_label']; ?></div>
            <div class="feedback-slider__label"><?= $args['higher_label']; ?></div>
        </div>
    <?php endif; ?>
</div>