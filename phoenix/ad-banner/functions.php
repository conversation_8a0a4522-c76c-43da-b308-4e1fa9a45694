<?php

function create_post_type_ad_banner() {
    $labels = [
        'name'                  => _x('AD Banner', 'Post general name'),
        'singular_name'         => _x('AD Banner', 'Post singular name'),
        'add_new'               => __('Create an AD Banner'),
        'add_new_item'          => __('Create an AD Banner'),
        'edit_item'             => __('Edit an AD Banner'),
        'new_item'              => __('Create New AD Banner'),
        'all_items'             => __('All AD Banners'),
        'view_item'             => __('View AD Banner'),
        'search_items'          => __('Search AD Banners'),
        'not_found'             => __('No AD Banner found'),
        'not_found_in_trash'    => __('No AD Banner found in the Trash'),
        'parent_item_location'  =>  '',
        'menu_name'             =>  'AD Banner',
    ];

    $args = [
        'labels'                => $labels,
        'description'           => 'AD Banner',
        'public'                => true,
        'publicly_queryable'    => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'query_var'             => true,
        'exclude_from_search'   => true,
        'menu_position'         => 10,
        'supports'              => array('title', 'custom-fields', 'revisions', 'editor'),
        'has_archive'           => false,
        'menu_icon'             => 'dashicons-format-image',
        'show_in_rest'          => true,
    ];
    register_post_type(AD_BANNER_SLUG, $args);
}
add_action('init', 'create_post_type_ad_banner');

// Remove 'ad-banner' from permalink -> occurs during campaign creation
add_filter('post_type_link', 'remove_ad_banner_slug', 10, 3);
function remove_ad_banner_slug($post_link, $post, $leavename) {
    if (AD_BANNER_SLUG != $post->post_type || 'publish' != $post->post_status)
        return $post_link;

    $post_link = str_replace('/' . $post->post_type . '/', '/', (string) $post_link);
    return $post_link;
}

// Gutenberg Blocks Category
function filter_block_categories_ad_banner($block_categories, $editor_context) {
    if (!empty($editor_context->post)) {
        array_unshift(
            $block_categories,
            [
                'slug'  => AD_BANNER_BLOCK_CATEGORY_SLUG,
                'title' => __('AD Banner'),
                'icon'  => null,
            ]
        );
    }
    return $block_categories;
}
add_filter('block_categories_all', 'filter_block_categories_ad_banner', 10, 2);

// Gutenberg Blocks includer
function register_ad_banner_blocks() {
    // register all blocks from the 'ad-banner/blocks' folder
    $blocks = glob(get_template_directory() . "/ad-banner/blocks/*", GLOB_ONLYDIR);
    if (is_array($blocks)) {
        foreach ($blocks as $block) {
            register_block_type($block);
        }
    }
}
add_action('init', 'register_ad_banner_blocks');


/**
 * Registers the block using the metadata loaded from the `block.json` file.
 * Behind the scenes, it registers also all assets so they can be enqueued
 * through the block editor in the corresponding context.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-metadata/
 */

/**
 * Registers block scripts and styles.
 */
function phoenix_register_block_assets() {
  // Editor Script
  $path = "/ad-banner/blocks/canvas/";
  wp_register_script(
      'ad-banner-block-canvas-editor',
      get_template_directory_uri() . $path . 'index.js',
      array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
      filemtime(get_template_directory() . $path . 'index.js')
  );

  // Editor Style
  wp_register_style(
      'ad-banner-block-canvas-editor',
      get_template_directory_uri() . $path . 'style.css',
      array(),
      filemtime(get_template_directory() . $path . 'style.css')
  );

}
add_action('init', 'phoenix_register_block_assets');
