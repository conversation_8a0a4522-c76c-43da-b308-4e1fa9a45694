<?php
$title = get_field('title');
$ctaUrl = get_field('cta_url');
$questions = get_field('questions');
$translations = get_field('translation');
$rtpgSettings = get_field('rtpg_settings');

if (!player()->isLoggedIn()) {
    $notificationArgs = [
        'message' => $translations['please_login_notification'],
        'button' => [
            'type' => 'login',
            'text' => get_field('login', 'option'),
        ],
        'id' => 'alert_not_logged_in',
        'show' => true,
        'overlay' => true,
    ];
    get_template_part('components/notification', null, $notificationArgs);
}
