<?php
add_action('init', 'create_post_type_survey');
function create_post_type_survey()
{
    $labels = [
        'name'                  => __('Surveys'),
        'singular_name'         => __('Survey'),
        'add_new'               => __('Create Survey'),
        'add_new_item'          => __('Create Survey'),
        'edit_item'             => __('Edit Survey'),
        'new_item'              => __('Create Survey'),
        'all_items'             => __('All Survey'),
        'view_item'             => __('View Survey'),
        'search_items'          => __('Search Surveys'),
        'not_found'             => __('No Survey found'),
        'not_found_in_trash'    => __('No Survey found in the Trash'),
        'parent_item_location'  =>  '',
        'menu_name'             =>  'Surveys',
    ];

    $args = [
        'labels'                => $labels,
        'description'           => 'Surveys for collecting user feedback',
        'public'                => true,
        'publicly_queryable'    => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'query_var'             => true,
        'exclude_from_search'   => true,
        'menu_position'         => 11,
        'supports'              => array('title', 'revisions'),
        'has_archive'           => true,
        'menu_icon'             => 'dashicons-editor-help',
    ];
    register_post_type(SURVEY_SLUG, $args);
}

// Remove 'survey' from permalink -> occurs during survey creation
add_filter('post_type_link', 'remove_survey_slug', 10, 3);
function remove_survey_slug($post_link, $post, $leavename)
{
    if (SURVEY_SLUG != $post->post_type || 'publish' != $post->post_status)
        return $post_link;

    $post_link = str_replace('/' . $post->post_type . '/', '/', (string) $post_link);
    return $post_link;
}

include(locate_template('survey/functions-reports.php', false, false));
