<?php
define('SURVEY_DB', 'px_survey_answers');

// Create Money Drop Answers table in database
add_action('after_switch_theme', 'survey_activate_database');
function survey_activate_database() {
    global $wpdb;
    $table_name = SURVEY_DB;

    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `post_id` BIGINT(20) UNSIGNED NOT NULL,
        `pid` VARCHAR(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `data` text NOT NULL,
        `datetime` datetime DEFAULT current_timestamp() NOT NULL
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $wpdb->query($sql);
    if ($wpdb->last_error !== '')
        do_action('qm/error', 'Error creating table ' . $table_name . ': ' . $wpdb->last_error . ' Query: ' . $wpdb->last_query . ' Result: ' . $wpdb->last_result);
}

// Download Report button
add_action('post_submitbox_misc_actions', 'survey_report_button', 10, 1);
function survey_report_button($post) {
    global $wpdb;
    $screen = get_current_screen();

    if ($screen->post_type !== SURVEY_SLUG)
        return;

    $table_name = SURVEY_DB;
    $query = "SELECT COUNT(*) FROM {$table_name} WHERE post_id = {$post->ID}";

    $count = $wpdb->get_var($query);

?>
    <div id="major-publishing-actions" style="overflow:hidden">
        <div id="publishing-action" style="float: unset; text-align: left;">
            <span style="line-height: 30px;">Entries: <strong><?= $count ?></strong></span>
            <a style="float: right;" target="_blank" download href="<?= add_query_arg(['download' => 'report'], get_edit_post_link($post->ID)) ?>" class="button-primary acf-button">Download Report</a>
        </div>
    </div>
<?php
}

add_action('admin_notices', function () {
    $screen = get_current_screen();
    $template = get_the_primary_term(get_the_ID(), CAMPAIGN_TEMPLATE_SLUG);
    $templateSlug = null;
    if (!empty($template)) {
        $templateSlug = $template->slug;
    }

    if ($screen->post_type !== CAMPAIGN_SLUG || $templateSlug !== 'survey')
        return;

    $class          = 'notice notice-large notice-error is-dismissible';
    $notice_message = 'Do not modify the survey questions after the survey has been published. This will cause the report to be inconsistent.';
    printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
});

add_action('admin_init', 'get_survey_repot', 1, 0);
function get_survey_repot() {
    global $pagenow;
    if (is_admin() && $pagenow == 'post.php' && isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['post']) && isset($_GET['download']) && $_GET['download'] == 'report') {
        $postID = intval($_GET['post']);
        if (empty($postID)) {
            wp_die(__('Invalid post ID.'));
        }

        if (get_post_type($postID) == SURVEY_SLUG) {
            // Set headers for CSV download
            $filename = sanitize_title(get_the_title($postID)) . '-' . date('d-m-Y__H-i-s');

            header("Pragma: public");
            header("Expires: 0");
            header("Cache-Control: private", false);
            header("Content-type: text/csv");
            header("Content-Disposition: attachment; filename={$filename}.csv");

            $fp = fopen('php://output', 'w');

            // Get Answers
            global $wpdb;
            $table_name = SURVEY_DB;
            $results = $wpdb->get_results("SELECT * FROM `$table_name` WHERE `post_id` = {$postID}", ARRAY_A);

            // ------------------------------------------------------------------------------------------------------------------------------
            addPostDetailsToReport($fp, $postID);

            $questions = get_field('questions', $postID);
            $row = [];
            foreach ($questions as $key => $question) {
                $key++;
                $row[] = 'Question ' . $key;
                $row[] = 'Answer ' . $key;
                $row[] = 'Skip ' . $key;
            }

            fputcsv($fp, ['Survey Answers Report']);
            fputcsv($fp, array_merge(['ID', 'PID', 'Email', 'Date'], $row));

            // Output data
            foreach ($results as $result) {
                $data = unserialize($result['data']);
                $row = [];
                foreach ($data as $value) {
                    if (is_array($value['answer'])) {
                        $value['answer'] = implode('|', $value['answer']);
                    }
                    $row[] = $value['question'];
                    $row[] = $value['answer'];
                    $row[] = $value['skip'];
                }

                fputcsv($fp, array_merge([
                    $result['id'],
                    decryptedPID($result['pid']),
                    $result['email'],
                    $result['datetime'],
                ], $row));
            }

            fclose($fp);
            exit;
        }
    }
}

// AJAX handler to money drop answer sumbit
add_action('wp_ajax_submit_survey_anwser', 'submit_survey_anwser');
add_action('wp_ajax_nopriv_submit_survey_anwser', 'submit_survey_anwser');
function submit_survey_anwser() {
    if (!player()->isLoggedIn() || empty($_POST['post_id'])) {
        wp_send_json_error();
    }

    $questions = get_field('questions', $_POST['post_id']);
    $answers = isset($_POST['survey_questions']) ? $_POST['survey_questions'] : [];
    $data = [];
    foreach ($answers as $key => $answer) {
        $data[$key] = $answer;
        $data[$key]['question'] = $questions[$key - 1]['title'] ?? '';
    }

    $package = [
        'post_id'  => $_POST['post_id'],
        'pid'      => player()->getId(),
        'email'    => player()->getEmail(),
        'data'     => serialize($data),
        'datetime' => getNow()->format('Y-m-d H:i:s'),
    ];

    global $wpdb;
    if ($wpdb->insert(SURVEY_DB, $package)) {
        wp_send_json_success();
    }
    wp_send_json_error();
}
