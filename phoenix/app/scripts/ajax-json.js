/* Lint ------------------- */
/* global main_params, getCookie */

function postJson(path, data, domain) {
	const headers = new Headers();
	headers.append('Content-Type', 'application/json');
	const options = {
		headers,
		method: 'POST',
		mode: 'cors',
		cache: 'default',
		credentials: 'include',
		body: JSON.stringify(data)
	};

	return fetch(getUrl(path, domain), options)
		.then(response => {
			if (response.status === 204) {
				return undefined;
			}
			return response.json();
		})
		.catch(e => {
			// eslint-disable-next-line no-console
			console.log(`${e} for ajax call on ${path}`);

			return Promise.reject(e);
		});
}

function fetchJson(path, domain) {
	const headers = new Headers();
	headers.append('Content-Type', 'application/json');
	const options = {
		headers,
		method: 'GET',
		mode: 'cors',
		cache: 'default',
		credentials: 'same-origin',
	};

	return fetch(getUrl(path, domain), options)
		.then(response => {
			if (response.status === 204) {
				return undefined;
			}
			return response.json();
		})
		.catch(e => {
			// eslint-disable-next-line no-console
			console.log(`${e} for ajax call on ${path}`);

			return Promise.reject(e);
		});
}

const createUrlParameters = urlParams =>
	Object.keys(urlParams)
		.map(key => {
			const encodedKey = encodeURIComponent(key);
			const encodedDataKey = encodeURIComponent(urlParams[key]);
			return `${encodedKey}=${encodedDataKey}`;
		})
		.join('&');

function getUrl(path, domain, urlParams) {
	const API_BASE_PATH = domain || main_params.brand_url || '';
	if (urlParams === undefined) {
		return `${API_BASE_PATH}${path}`;
	}
	return `${API_BASE_PATH}${path}?${createUrlParameters(urlParams)}`;
}
