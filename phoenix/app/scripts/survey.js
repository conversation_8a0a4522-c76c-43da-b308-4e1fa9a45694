/* Lint ------------------- */
/* global documentReady, main_params, showStickyButtonEvent, hideStickyButtonEvent, closeModals */
documentReady(function () {
    if (document.body.classList.contains('survey')) {

        const questions = document.querySelectorAll('.js-survey-step');

        const progress_bar = document.querySelector('.js-survey-progress-bar');
        const display_progress_count = document.querySelector('.js-survey-progress-count');

        const display_review_count = document.querySelector('.js-survey-progress-review');
        let questions_review;
        let current_question_review = 1;
        let questions_review_count = 0;

        const error_message = document.querySelector('.js-survey-error-message');
        const submit_error_message = document.querySelector('.js-survey-submit-error-message');

        let survey_status = 'progress'; // 'progress', 'almost-done', 'review', 'done'
        let curtent_question = 1;
        let questions_count = questions.length;

        // pagination of the questions and  update progress bar
        let update_progress = () => {
            hide_error_message();

            progress_bar.style.setProperty('width', `${100 / questions_count * curtent_question}%`);
            display_progress_count.innerHTML = `${curtent_question}/${questions_count}`;

            if (curtent_question === 1) {
                document.querySelector('.js-survey-previous').classList.add('survey__action--hidden');
            } else {
                document.querySelector('.js-survey-previous').classList.remove('survey__action--hidden');
            }

            questions.forEach((question, index) => {
                if (index + 1 === curtent_question) {
                    question.classList.add('survey__step--active');
                } else {
                    question.classList.remove('survey__step--active');
                }
            });
        }

        // Show/hide appropriate headers, contents, and buttons based on the survey status
        let update_status = (new_status) => {
            survey_status = new_status;

            // Hide all headers, contents, and buttons
            let survey_headers = document.querySelectorAll('.survey__header');
            survey_headers.forEach(header => {
                header.classList.remove('survey__header--visible');
            });

            let survey_contents = document.querySelectorAll('.survey__content');
            survey_contents.forEach(content => {
                content.classList.remove('survey__content--visible');
            });

            let survey_buttons = document.querySelectorAll('.survey__buttons');
            survey_buttons.forEach(button => {
                button.classList.remove('survey__buttons--visible');
            });

            questions.forEach((question) => {
                question.classList.remove('survey__step--active');
            });

            // Show the appropriate header, content, and buttons based on the survey status
            switch (survey_status) {
                case 'progress':
                    document.querySelector('.js-survey-header-progress').classList.add('survey__header--visible');
                    document.querySelector('.js-survey-skip-mobile').classList.add('survey__header--visible');
                    document.querySelector('.js-survey-content-steps').classList.add('survey__content--visible');
                    document.querySelector('.js-survey-buttons-progress').classList.add('survey__buttons--visible');
                    break;

                case 'almost-done':
                    questions_review = document.querySelectorAll('.js-survey-step[data-step-skip="true"]');
                    questions_review_count = questions_review.length;
                    if (questions_review_count > 0) {
                        document.querySelector('.js-survey-review-answers').classList.remove('survey__action--hidden');
                        let hint_almost_done = document.querySelector('.js-survey-review-hint-almost-done');
                        hint_almost_done.classList.remove('survey__hint--hidden');
                        hint_almost_done.innerHTML = hint_almost_done.getAttribute('data-text').replace('{%d}', questions_review_count);
                        document.querySelector('.js-survey-submit-hint-almost-done').classList.add('survey__hint--hidden');

                    } else {
                        document.querySelector('.js-survey-review-answers').classList.add('survey__action--hidden');
                        document.querySelector('.js-survey-review-hint-almost-done').classList.add('survey__hint--hidden');
                        document.querySelector('.js-survey-submit-hint-almost-done').classList.remove('survey__hint--hidden');
                    }
                    document.querySelector('.js-survey-content-almost-done').classList.add('survey__content--visible');
                    document.querySelector('.js-survey-buttons-almost-done').classList.add('survey__buttons--visible');
                    break;

                case 'review':
                    document.querySelector('.js-survey-header-review').classList.add('survey__header--visible');
                    document.querySelector('.js-survey-skip-mobile').classList.add('survey__header--visible');
                    document.querySelector('.js-survey-content-steps').classList.add('survey__content--visible');
                    document.querySelector('.js-survey-buttons-progress').classList.add('survey__buttons--visible');
                    break;

                case 'done':
                    document.querySelector('.js-survey-content-done').classList.add('survey__content--visible');
                    document.querySelector('.js-survey-buttons-done').classList.add('survey__buttons--visible');
                    break
            }
        }

        // pagination of the reviewed questions and update review progress count
        let update_review = () => {
            hide_error_message();

            display_review_count.innerHTML = `${current_question_review}/${questions_review_count}`;

            if (current_question_review === 1) {
                document.querySelector('.js-survey-previous').classList.add('survey__action--hidden');
            } else {
                document.querySelector('.js-survey-previous').classList.remove('survey__action--hidden');
            }

            questions_review.forEach((question, index) => {
                if (index + 1 === current_question_review) {
                    question.classList.add('survey__step--active');
                } else {
                    question.classList.remove('survey__step--active');
                }
            });
        }

        let get_current_step = () => {
            return document.querySelector('.js-survey-step.survey__step--active');
        }

        let set_current_step_skip = (skip_value) => {
            const current_step = get_current_step();
            current_step.setAttribute('data-step-skip', skip_value);
            current_step.querySelector('.js-survey-step_skip').value = skip_value;
        }

        // Validate the current step
        let validate_current_step = () => {
            const current_step = get_current_step();
            switch (current_step.getAttribute('data-question-type')) {
                case 'feedback-slider': {
                    const feedback_slider = current_step.querySelector('input[type="range"]');
                    if (feedback_slider.value == '') return false;
                }
                    break;

                case 'multi-select-checkbox': {
                    const multi_select_checkbox = current_step.querySelectorAll('input[type="checkbox"]:checked');
                    console.log(multi_select_checkbox, multi_select_checkbox !== undefined || multi_select_checkbox.length === 0);
                    if (multi_select_checkbox.length == 0) return false;
                }
                    break;

                case 'yes-no-radio': {
                    const yes_no_radio = current_step.querySelectorAll('input[type="radio"]:checked');
                    if (yes_no_radio.length == 0) return false;
                }
                    break;

                case 'textarea': {
                    const textarea = current_step.querySelector('textarea');
                    if (textarea.value.trim() == '') return false;
                }
                    break;
            }

            return true;
        }

        let show_error_message = () => {
            error_message.classList.add('survey__error--visible');
            setTimeout(() => {
                hide_error_message();
            }, 3000);
        }

        let hide_error_message = () => {
            error_message.classList.remove('survey__error--visible');
        }

        let show_submit_error_message = () => {
            submit_error_message.classList.add('survey__error--visible');
            setTimeout(() => {
                submit_error_message.classList.remove('survey__error--visible');
            }, 5000);
        }

        update_progress();

        // Buttons handlers
        const skip_btns = document.querySelectorAll('.js-survey-skip');
        skip_btns.forEach(function (skip_btn) {
            skip_btn.addEventListener("click", function (e) {
                e.preventDefault();
                set_current_step_skip(true);

                switch (survey_status) {
                    case 'progress':
                        if (curtent_question == questions_count) {
                            update_status('almost-done');
                        } else {
                            curtent_question++;
                            update_progress();
                        }
                        break;
                    case 'review':
                        if (current_question_review == questions_review_count) {
                            update_status('almost-done');
                        } else {
                            current_question_review++;
                            update_review();
                        }
                        break;
                }
            });
        });

        const previous_btn = document.querySelector('.js-survey-previous');
        previous_btn.addEventListener("click", function (e) {
            e.preventDefault();
            switch (survey_status) {
                case 'progress':
                    if (curtent_question > 1) {
                        curtent_question--;
                        update_progress();
                    }
                    break;
                case 'review':
                    if (current_question_review > 1) {
                        current_question_review--;
                        update_review();
                    }
                    break;
            }

            set_current_step_skip(false);
        });

        const next_btn = document.querySelector('.js-survey-next');
        next_btn.addEventListener("click", function (e) {
            e.preventDefault();
            if (!validate_current_step()) {
                show_error_message()
            } else {
                set_current_step_skip(false);

                switch (survey_status) {
                    case 'progress':
                        if (curtent_question == questions_count) {
                            update_status('almost-done');
                        } else {
                            curtent_question++;
                            update_progress();
                        }
                        break;
                    case 'review':
                        if (current_question_review == questions_review_count) {
                            update_status('almost-done');
                        } else {
                            current_question_review++;
                            update_review();
                        }
                        break;
                }
            }
        });

        const review_btn = document.querySelector('.js-survey-review-answers');
        review_btn.addEventListener("click", function (e) {
            e.preventDefault();
            current_question_review = 1;
            update_status('review');
            update_review();
        });


        // Submit survey answers
        const submit_btn = document.querySelector('.js-survey-submit');
        submit_btn.addEventListener("click", function (e) {
            e.preventDefault();
            submit_btn.classList.add('btn--disabled');
            review_btn.classList.add('btn--disabled');
            document.querySelector('.js-survey-loader-icon').classList.remove('survey__question-icon--hidden');


            let formData = new FormData(document.querySelector('#js-survey-form'));
            formData.append('action', 'submit_survey_anwser');
            formData.append('post_id', document.querySelector('body.survey').getAttribute('data-post_id'));

            var xmlHttp = new XMLHttpRequest();
            xmlHttp.onreadystatechange = function () {
                if (xmlHttp.readyState == 4) {
                    if (xmlHttp.status == 200) {
                        try {
                            let response = JSON.parse(xmlHttp.response);
                            if (response && response.success == true) {
                                update_status('done');
                            } else {
                                show_submit_error_message();
                            }
                        } catch (err) {
                            show_submit_error_message();
                        }
                    } else {
                        show_submit_error_message();
                    }

                    submit_btn.classList.remove('btn--disabled');
                    review_btn.classList.remove('btn--disabled');
                    document.querySelector('.js-survey-loader-icon').classList.add('survey__question-icon--hidden');
                }
            }
            xmlHttp.open('post', main_params.ajax_url);
            xmlHttp.send(formData);

        });
    }
});