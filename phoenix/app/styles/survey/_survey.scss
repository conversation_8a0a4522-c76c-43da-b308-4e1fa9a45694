.survey {
    &__middle {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    &__container {
        background-color: $survey-background;
        display: flex;
        flex-direction: column;
        height: 100%;
        @include gap($gutter);
        text-align: center;
        box-sizing: border-box;
        transition: all $transition;
        color: $survey-color;

        @include to("md") {
            background-color: $survey-background-mobile;
            padding: $gutter;
            @include min-full-screen();
        }

        @include from("md") {
            max-width: 477px;
            width: 100%;
            margin: 100px auto;
            padding: 40px;
            border-radius: $gutter;
            box-shadow: 0 4px 16px rgba($color-primary-black, 0.08);
        }
    }

    &__header {
        display: none;


        &--visible {
            display: block;
        }
    }

    &__title {
        @include get-typography("heading-5");
    }

    &__progress {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        @include gap($gutter);

        &__count {
            @include get-typography("caption-bold");
            white-space: nowrap;
        }

        &__bar {
            width: 100%;
            height: 7px;
            background-color: $survey-progress-bar-background;
            border-radius: 5px;
            position: relative;

            &__done {
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 100%;
                background-color: $survey-progress-bar-done;
                border-radius: 5px;
                transition: width $transition;
            }
        }
    }

    &__question-icon {
        display: block;
        margin: 6px auto;
        width: 56px;
        height: 56px;
        color: $survey-question-icon-color;
        transition: all $transition;

        svg {
            width: 100%;
            height: 100%;

            path {
                fill: currentColor;
            }

            circle {
                stroke: currentColor;
            }
        }

        &--hidden {
            height: 0px;
            width: 0px;
            margin: 0 auto;
            opacity: 0;
            overflow: hidden
        }
    }

    &__question {
        @include get-typography("heading-2");
    }

    &__hint {
        @include get-typography("caption");
        color: $color-font-supportive;
        max-width: 300px;
        margin: 6px auto 0;

        &--step {
            margin-top: 20px;
        }

        &--hidden {
            display: none;
        }
    }

    &__input {
        margin-top: 20px;
    }

    &__error {
        $survey_error: &;

        @include get-typography("caption");
        color: $color-system-error;
        margin-top: 20px;
        opacity: 0;
        transition: opacity $transition;

        &--visible {
            opacity: 1;
        }

        &:has(#{$survey_error}__icon) {
            display: flex;
            align-items: center;
            justify-content: center;
            @include gap(6px);
        }

        &__icon {
            width: 16px;
            height: 16px;

            svg {
                width: 100%;
                height: 100%;

                path {
                    fill: currentColor;
                }
            }
        }
    }

    &__content {
        flex: 1;
        display: none;
        flex-direction: column;
        justify-content: center;

        &--steps {
            @include from("md") {
                min-height: 420px;
            }
        }

        &--almost-done,
        &--done {
            @include from("md") {
                min-height: 190px;
            }
        }

        &--visible {
            display: flex;
        }
    }

    &__step {
        height: 0;
        overflow: hidden;
        opacity: 0;
        transition: all $transition;

        &--active {
            opacity: 1;
            height: auto;
        }
    }

    &__buttons {
        justify-content: space-between;
        align-items: center;
        @include gap($gutter);
        display: none;

        &--almost-done {
            flex-direction: column;
            @include from("md") {
                flex-direction: row;
            }
        }

        &--visible {
            display: flex;
        }
    }

    &__action {
        flex: 1;
        width: 100%;

        &--skip {
            &--mobile {
                text-align: right;

                @include from("md") {
                    display: none;
                }
            }

            &--dektop {
                text-align: left;
                display: none;

                @include from("md") {
                    display: block;
                }
            }
        }

        &--hidden {
            display: none !important;
        }
    }

    &__skip {
        @include get-typography("label");
        text-decoration: none;
        background-color: transparent;
        border: none;
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }
}
