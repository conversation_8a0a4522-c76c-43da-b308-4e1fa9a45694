import gulp from 'gulp';
import sass from 'gulp-dart-sass';      // SASS Compiler
import sassGlob from 'gulp-sass-glob';  // For gulp-sass to use glob imports, example: @import 'blocks/**/*.scss';
import uglify from 'gulp-uglify';       // Minify .js files
import cleanCss from 'gulp-clean-css';  // Minify .css files
import eslint from 'gulp-eslint';       // To flag programming errors, bugs, stylistic errors and suspicious constructs in .js files
import gulpStylelint from '@ronilaukkarinen/gulp-stylelint'; // To lint SCSS files
import imagemin from 'gulp-imagemin';   // Minify PNG, JPEG, GIF and SVG images
import size from 'gulp-size';           // Display the size of files, outputs etc.
import concat from 'gulp-concat';       // Concatenate files, or outputs etc.
import babel from 'gulp-babel';         // Converts ES6 to ES5 to support more browsers and platforms
import { deleteAsync } from 'del';      // Removes files and folders
import gulplog from 'gulplog';          // Log things, prefixed with a timestamp
import touch from 'gulp-touch-cmd';     // Update mtime on files so BrowserSync can read latest modified timestamp and refresh
import fs from 'fs';
import download from 'gulp-download';
import filter from 'gulp-filter';

const { series, src, dest, task } = gulp;

var sourcemaps = false;
const themes = [
    '888',
    'casinostugan',
    'casinostuen',
    'cherrycasino',
    'comeon',
    'euroslots',
    'folkeriket',
    'galaksino',
    'getlucky',
    'hajper',
    'lyllo',
    'mobilautomaten',
    'mobilebet',
    'nopeampi',
    'norgesspill',
    'phoenix',
    'pzbuk',
    'snabbare',
    'spinon',
    'sunmaker',
    'suomikasino',
    'wespin',
];
const parentTheme = 'phoenix';

// Gulp tasks
task('showSourceMaps', function (cb) {
    gulplog.info('Sourcemaps added to assest');
    sourcemaps = true;
    return cb();
});

task('scripts', function () {
    return src([`${parentTheme}/app/scripts/**/*.js`, `!${parentTheme}/app/scripts/vendor/**`], { sourcemaps: sourcemaps })
        .pipe(eslint())
        .pipe(babel())
        .pipe(eslint.format())
        .pipe(eslint.failAfterError())
        .pipe(src(`${parentTheme}/app/scripts/vendor/*.js`, { sourcemaps: sourcemaps }))
        .pipe(uglify())
        .pipe(concat('main.js'))
        .pipe(size({ showFiles: true }))
        .pipe(dest(`./${parentTheme}/dist`, { sourcemaps: sourcemaps }))
        .pipe(touch());
});

task('admin-global-scripts', function () {
    return src(`${parentTheme}/admin/scripts/global/**/*.js`, { sourcemaps: sourcemaps })
        .pipe(eslint())
        .pipe(babel())
        .pipe(eslint.format())
        .pipe(eslint.failAfterError())
        .pipe(uglify())
        .pipe(concat('admin.min.js'))
        .pipe(size({ showFiles: true }))
        .pipe(dest(`./${parentTheme}/dist`, { sourcemaps: sourcemaps }))
        .pipe(touch());
});

task('admin-post-scripts', function () {
    return src(`${parentTheme}/admin/scripts/post/**/*.js`, { sourcemaps: sourcemaps })
        .pipe(eslint())
        .pipe(babel())
        .pipe(eslint.format())
        .pipe(eslint.failAfterError())
        .pipe(uglify())
        .pipe(concat('admin-post.min.js'))
        .pipe(size({ showFiles: true }))
        .pipe(dest(`./${parentTheme}/dist`, { sourcemaps: sourcemaps }))
        .pipe(touch());
});

task('admin-scripts', series(
    'admin-global-scripts',
    'admin-post-scripts',
));

themes.forEach(theme => {
    task(`clean-${theme}`, function () {
        return deleteAsync(`${theme}/dist`);
    });

    task(`styles-${theme}`, function () {
        return src([
            `${theme}/app/styles/page.scss`,
            `${theme}/app/styles/campaign.scss`,
            `${theme}/app/styles/blog.scss`,
            `${theme}/app/styles/games.scss`,
            `${theme}/app/styles/help-page.scss`,
            `${theme}/app/styles/survey.scss`,
            `${theme}/app/styles/responsible-gaming.scss`,
            `${theme}/app/styles/ad-banner.scss`
        ], { allowEmpty: true, sourcemaps: sourcemaps })
            .pipe(sassGlob())
            .pipe(gulpStylelint({
                failAfterError: true,
                reporters: [
                    {
                        formatter: 'string',
                        console: true
                    }
                ]
            }))
            .pipe(sass().on('error', sass.logError))
            .pipe(cleanCss())
            .pipe(size({ showFiles: true }))
            .pipe(dest(`${theme}/dist`, { sourcemaps: sourcemaps }))
            .pipe(touch());
    });

    task(`images-${theme}`, function () {
        return src(`${theme}/app/images/**/*`, { encoding: false })
            .pipe(imagemin())
            .pipe(size({ showFiles: true }))
            .pipe(dest(`${theme}/dist/images`));
    });

    task(`fonts-${theme}`, function (cb) {
        let fontFolder = `${theme}/app/fonts`;

        if (fs.existsSync(fontFolder)) {
            return src(fontFolder + '/**/*.{eot,svg,ttf,woff,woff2}')
                .pipe(size({ showFiles: true }))
                .pipe(dest(`${theme}/dist/fonts`));
        } else {
            gulplog.info('No font folder.');
            return cb();
        }

    });

    task(`admin-styles-${theme}`, function () {
        return src(`${theme}/admin/styles/main.scss`, { allowEmpty: true, sourcemaps: sourcemaps })
            .pipe(sassGlob())
            .pipe(gulpStylelint({
                failAfterError: true,
                reporters: [
                    {
                        formatter: 'string',
                        console: true
                    }
                ]
            }))
            .pipe(sass().on('error', sass.logError))
            .pipe(cleanCss())
            .pipe(concat('admin.min.css'))
            .pipe(size({ showFiles: true }))
            .pipe(dest(`${theme}/dist`, { sourcemaps: sourcemaps }))
            .pipe(touch());
    });

    task(`build-${theme}`, series(
        `styles-${theme}`,
        `images-${theme}`,
        `fonts-${theme}`,
        `admin-styles-${theme}`,
    ));

    task(`${theme}`, series(
        `clean-${theme}`,
        `scripts`,
        `admin-scripts`,
        `build-${theme}`
    ));

    task(`dev-${theme}`, series(
        `showSourceMaps`,
        `${theme}`
    ));

    task(`watch-${theme}`, function () {
        gulp.watch([`${theme}/app/styles/**`, `${parentTheme}/app/styles/**`], series(`styles-${theme}`, `admin-styles-${theme}`));
        gulp.watch([`${theme}/admin/styles/**`, `${parentTheme}/admin/styles/**`], series(`admin-styles-${theme}`));
        gulp.watch(`${parentTheme}/app/scripts/**`, series('scripts'));
        gulp.watch(`${parentTheme}/admin/scripts/**`, series('admin-scripts'));
    });

    task(`watch-${theme}`, series(
        `dev-${theme}`,
        `watch-${theme}`
    ));
});

task('clean', function () {
    return deleteAsync(['*/dist', 'phoenix/vectors/logos/payment-cdn/*']);
});

task('download-payment-logos', function () {
    let paymentLogosUrl = [];
    [
        'airtel',
        'applepay',
        'astropay',
        'axis',
        'bank',
        'bankid',
        'bitcoin',
        'blik',
        'brite',
        'dotpay',
        'ecashout',
        'ecopayz',
        'entropay',
        'euteller',
        'ezeewallet',
        'giropay',
        'hdfc',
        'icici',
        'ideal',
        'idebit',
        'interac',
        'instadebit',
        'imoje',
        'iwallet',
        'jcb',
        'jetongo',
        'jio',
        'line',
        'luxon',
        'maestro',
        'mastercard',
        'mifinity',
        'mobikwik',
        'mobilepay',
        'mojeid',
        'much-better',
        'netbanking',
        'neteller',
        'neosurf',
        'nordea',
        'orientalwallet',
        'paydo',
        'paypal',
        'paysafe',
        'paytm',
        'phonepe',
        'przelewy',
        'revolut',
        'sbi',
        'siirto',
        'siru',
        'skrill',
        'skrill1tap',
        'sms-voucher',
        'sofort',
        'sumopay',
        'swish',
        'tether-usdt',
        'tigerpay',
        'trustly',
        'transactionlink',
        'tpay',
        'upi',
        'venus-point',
        'visa',
        'visabyvoucher',
        'zimpler',
    ].forEach(url => {
        paymentLogosUrl.push(`https://d3qr93f6ngnoje.cloudfront.net/svg-logos/4/${url}.svg`);
    });

    return download(paymentLogosUrl).on('response', function(response) {
            gulplog.info(response.statusCode) // 200
            gulplog.info(response.headers['content-type']) // 'image/png'
        }).pipe(filter(function (file) {
            let contents = file.contents.toString();
            return !contents.match('<Code>AccessDenied</Code>');
        }))
        .pipe(gulp.dest("phoenix/vectors/logos/payment-cdn/"));
});

['styles', 'images', 'fonts', 'admin-styles'].forEach(themeTask => {
    task(themeTask, series(themes.map(theme => `${themeTask}-${theme}`)))
});

task('default', series('clean', 'scripts', 'admin-scripts', series(...themes.map(theme => `build-${theme}`))));

task('dev', series(
    'showSourceMaps',
    'default',
    'download-payment-logos',
));

task('watch', function () {
    gulp.watch([`*/app/styles/**`], series('styles', 'admin-styles'));
    gulp.watch([`*/admin/styles/**`], series('admin-styles'));
    gulp.watch(`${parentTheme}/app/scripts/**`, series('scripts'));
    gulp.watch(`${parentTheme}/admin/scripts/**`, series('admin-scripts'));
});

task('watch', series(
    'dev',
    'watch'
));